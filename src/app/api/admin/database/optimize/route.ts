import { NextRequest, NextResponse } from 'next/server';
import { withSimpleSecurity } from '@/lib/simple-security';
import { dbOptimization } from '@/lib/services/databaseOptimization';
import { connectionPool } from '@/lib/database/connection-pool';
import { queryOptimizer } from '@/lib/database/query-optimizer';

async function handlePOST(req: NextRequest): Promise<NextResponse> {
  try {
    console.log('🚀 Starting database optimization...');
    
    // Run comprehensive database optimization
    const optimizationResult = await dbOptimization.runOptimization();
    
    // Get updated performance metrics
    const performanceReport = await dbOptimization.getPerformanceReport();
    
    // Get connection pool stats
    const connectionStats = connectionPool.getStats();
    
    // Get query optimizer stats
    const queryStats = queryOptimizer.getStats();
    
    // Generate fresh recommendations
    const recommendations = await dbOptimization.getRecommendations();
    
    console.log('✅ Database optimization completed successfully');
    
    return NextResponse.json({
      success: true,
      message: 'Database optimization completed successfully',
      data: {
        optimization: {
          applied: optimizationResult.optimizations,
          timestamp: new Date().toISOString(),
          duration: optimizationResult.duration
        },
        performance: {
          before: optimizationResult.before,
          after: performanceReport,
          improvement: {
            queryTime: optimizationResult.before.averageQueryTime - performanceReport.averageQueryTime,
            cacheHitRate: performanceReport.cache.hitRate - optimizationResult.before.cache.hitRate
          }
        },
        connections: {
          active: connectionStats.activeConnections,
          total: connectionStats.totalConnections,
          health: connectionStats.failedConnections === 0 ? 'healthy' : 'degraded',
          averageTime: connectionStats.averageConnectionTime
        },
        queries: {
          total: queryStats.totalQueries,
          averageTime: queryStats.averageExecutionTime,
          cacheHitRate: queryStats.cacheHitRate
        },
        recommendations: recommendations,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Database optimization failed:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Database optimization failed',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Apply security middleware and export
export const POST = withSimpleSecurity(handlePOST, {
  requireAuth: true,
  requireAdmin: true,
  rateLimit: {
    requests: 5,
    window: 300000 // 5 minutes
  }
});

// Only allow POST method
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to run database optimization.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to run database optimization.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to run database optimization.' },
    { status: 405 }
  );
}
