# Unified AI Service Documentation

## Overview

The Unified AI Service provides a centralized, improved interface for all AI operations in the FAAFO Career Platform. It replaces the previous direct Gemini service usage with a more robust, configurable, and maintainable solution.

## Key Features

- **Centralized Configuration**: All AI settings managed through environment variables
- **Improved Caching**: Automatic caching with configurable TTL
- **Consistent Error Handling**: Standardized error messages and recovery
- **Task-Specific Optimization**: Different configurations for different AI tasks
- **Better Abstraction**: Clean interface that hides implementation details

## Environment Variables

### Required Variables

```bash
# Google Gemini API Key (Required)
GOOGLE_GEMINI_API_KEY=your_gemini_api_key_here
```

### Optional Configuration Variables

```bash
# AI Model Configuration
AI_MODEL=gemini-1.5-flash                    # Default: gemini-1.5-flash
AI_MAX_TOKENS=2048                           # Default: 2048
AI_TEMPERATURE=0.7                           # Default: 0.7

# Caching Configuration
AI_CACHE_ENABLED=true                        # Default: true
AI_CACHE_TTL=3600                           # Default: 3600 (1 hour)
```

## Task-Specific Configurations

The service automatically applies optimized configurations for different AI tasks:

| Task Type | Temperature | Max Tokens | Use Case |
|-----------|-------------|------------|----------|
| `resume_analysis` | 0.3 | 2048 | Analyzing resumes for structured feedback |
| `career_recommendations` | 0.7 | 1024 | Generating personalized career suggestions |
| `skills_analysis` | 0.4 | 1536 | Analyzing skill gaps and learning paths |
| `interview_prep` | 0.6 | 2048 | Interview preparation content |
| `personality_analysis` | 0.5 | 1536 | Personality trait analysis |
| `career_fit_analysis` | 0.6 | 1024 | Career fit scoring |
| `skill_gap_analysis` | 0.4 | 1536 | Detailed skill gap analysis |
| `learning_style_analysis` | 0.5 | 1024 | Learning preference analysis |
| `market_trend_analysis` | 0.3 | 1536 | Market trend insights |
| `personalized_content` | 0.7 | 2048 | General personalized content |

## API Usage

### Basic Usage

```typescript
import { unifiedAIService } from '@/lib/unifiedAIService';

// Analyze a resume
const result = await unifiedAIService.analyzeResume(resumeText, userId);

// Generate career recommendations
const recommendations = await unifiedAIService.generateCareerRecommendations(
  assessmentData,
  currentSkills,
  preferences,
  userId
);

// Analyze skills gap
const skillsAnalysis = await unifiedAIService.analyzeSkillsGap(
  currentSkills,
  targetCareerPath,
  experienceLevel,
  userId
);
```

### Response Format

All methods return a standardized `AIResponse` object:

```typescript
interface AIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  cached?: boolean;
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
    model?: string;
    temperature?: number;
    generatedAt: string;
  };
}
```

### Health Check

```typescript
const isHealthy = await unifiedAIService.healthCheck();
```

### Cache Management

```typescript
// Clear all AI cache
await unifiedAIService.clearCache();
```

## Caching Strategy

The service implements automatic caching with the following features:

- **Cache Keys**: Generated based on user ID and content hash
- **TTL**: Configurable via `AI_CACHE_TTL` environment variable
- **Prefix**: All cache keys prefixed with `ai:`
- **Automatic**: No manual cache management required

### Cache Key Patterns

- Resume Analysis: `ai:resume_analysis_{userId}_{contentHash}`
- Career Recommendations: `ai:career_recommendations_{userId}_{contextHash}`
- Skills Analysis: `ai:skills_analysis_{userId}_{contextHash}`
- Personalized Content: `ai:{taskType}_{userId}_{contextHash}`

## Error Handling

The service provides consistent error handling with user-friendly messages:

- **Quota Exceeded**: "AI service quota exceeded. Please try again later."
- **Safety Filter**: "Content blocked by AI safety filters."
- **Rate Limit**: "AI service rate limit exceeded. Please try again later."
- **Generic**: "AI service temporarily unavailable"

## Migration from Previous Service

### Before (geminiService)

```typescript
import { geminiService } from '@/lib/services/geminiService';

const result = await geminiService.analyzeResume(resumeText, userId);
```

### After (unifiedAIService)

```typescript
import { unifiedAIService } from '@/lib/unifiedAIService';

const result = await unifiedAIService.analyzeResume(resumeText, userId);
```

## Benefits

1. **Simplified Configuration**: Single source of truth for AI settings
2. **Better Performance**: Optimized caching reduces API calls
3. **Improved Reliability**: Consistent error handling and recovery
4. **Easier Maintenance**: Centralized service for all AI operations
5. **Cost Optimization**: Intelligent caching reduces API usage

## Monitoring and Debugging

### Health Check Endpoint

```
GET /api/ai/health?service=ai
```

### Test AI Service

```
POST /api/ai/health?action=test_ai
```

### Clear Cache

```
POST /api/ai/health?action=clear_cache
```

## Best Practices

1. **Always provide userId** for better caching and analytics
2. **Handle errors gracefully** using the standardized error messages
3. **Monitor cache hit rates** to optimize performance
4. **Use appropriate task types** for optimal AI configurations
5. **Implement proper rate limiting** in your application layer

## Troubleshooting

### Common Issues

1. **"GOOGLE_GEMINI_API_KEY is required"**
   - Ensure the environment variable is set correctly

2. **High API usage**
   - Check if caching is enabled (`AI_CACHE_ENABLED=true`)
   - Verify cache TTL is appropriate for your use case

3. **Slow responses**
   - Check if cache is working properly
   - Consider reducing `AI_MAX_TOKENS` for faster responses

4. **Inconsistent results**
   - Verify task-specific configurations are appropriate
   - Check if `AI_TEMPERATURE` is set correctly for your use case

## Future Enhancements

The unified service is designed to be extensible for future improvements:

- Support for additional AI providers
- Advanced caching strategies
- Request queuing and rate limiting
- Performance analytics and monitoring
- A/B testing capabilities
