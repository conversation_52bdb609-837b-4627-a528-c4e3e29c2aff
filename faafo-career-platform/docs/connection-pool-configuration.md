# Connection Pool Configuration Guide

## Overview

This guide provides comprehensive configuration options for the production-optimized database connection pool system implemented in Task 3.3.

## Environment Variables

### Basic Configuration

```env
# Database Connection
DATABASE_URL="postgresql://username:password@host:port/database"
DATABASE_TIER="basic"  # hobby, basic, standard, premium, enterprise
NODE_ENV="production"  # development, staging, production

# Connection Pool Settings
DB_MIN_CONNECTIONS=2
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=10000
DB_IDLE_TIMEOUT=30000
DB_MAX_LIFETIME=3600000
DB_HEALTH_CHECK_INTERVAL=30000
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=1000

# Production Features
DB_ENABLE_AUTO_SCALING=true
DB_CONNECTION_LEAK_DETECTION=true
DB_CONNECTION_LEAK_TIMEOUT=300000
DB_SLOW_QUERY_THRESHOLD=1000
DB_ENABLE_METRICS=true
DB_METRICS_RETENTION_HOURS=24
DB_ENABLE_CONNECTION_WARMUP=true
DB_WARMUP_CONNECTIONS=2

# Advanced Features
DB_ENABLE_LOAD_BALANCING=false
DB_READ_REPLICA_RATIO=0.7
```

## Database Tier Configurations

### Hobby Tier
**Recommended for**: Development, small personal projects
```env
DATABASE_TIER=hobby
DB_MIN_CONNECTIONS=1
DB_MAX_CONNECTIONS=5
DB_CONNECTION_TIMEOUT=5000
DB_IDLE_TIMEOUT=30000
DB_HEALTH_CHECK_INTERVAL=60000
DB_ENABLE_AUTO_SCALING=false
```

### Basic Tier
**Recommended for**: Small applications, staging environments
```env
DATABASE_TIER=basic
DB_MIN_CONNECTIONS=2
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=10000
DB_IDLE_TIMEOUT=30000
DB_HEALTH_CHECK_INTERVAL=30000
DB_ENABLE_AUTO_SCALING=true
```

### Standard Tier
**Recommended for**: Production applications with moderate load
```env
DATABASE_TIER=standard
DB_MIN_CONNECTIONS=5
DB_MAX_CONNECTIONS=50
DB_CONNECTION_TIMEOUT=15000
DB_IDLE_TIMEOUT=60000
DB_HEALTH_CHECK_INTERVAL=30000
DB_ENABLE_AUTO_SCALING=true
DB_ENABLE_CONNECTION_WARMUP=true
```

### Premium Tier
**Recommended for**: High-traffic production applications
```env
DATABASE_TIER=premium
DB_MIN_CONNECTIONS=10
DB_MAX_CONNECTIONS=100
DB_CONNECTION_TIMEOUT=20000
DB_IDLE_TIMEOUT=120000
DB_HEALTH_CHECK_INTERVAL=15000
DB_ENABLE_AUTO_SCALING=true
DB_ENABLE_CONNECTION_WARMUP=true
DB_ENABLE_LOAD_BALANCING=true
```

### Enterprise Tier
**Recommended for**: Large-scale enterprise applications
```env
DATABASE_TIER=enterprise
DB_MIN_CONNECTIONS=20
DB_MAX_CONNECTIONS=200
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=300000
DB_HEALTH_CHECK_INTERVAL=10000
DB_ENABLE_AUTO_SCALING=true
DB_ENABLE_CONNECTION_WARMUP=true
DB_ENABLE_LOAD_BALANCING=true
DB_READ_REPLICA_RATIO=0.8
```

## Platform-Specific Configurations

### Vercel Deployment
```env
# Vercel with Neon/PlanetScale
DATABASE_URL="postgresql://..."
DATABASE_TIER=standard
DB_MAX_CONNECTIONS=20  # Vercel function limit
DB_CONNECTION_TIMEOUT=10000
DB_ENABLE_AUTO_SCALING=false  # Serverless doesn't need auto-scaling
DB_ENABLE_CONNECTION_WARMUP=false  # Not beneficial in serverless
```

### Railway Deployment
```env
# Railway with PostgreSQL
DATABASE_URL="postgresql://..."
DATABASE_TIER=standard
DB_MAX_CONNECTIONS=50
DB_CONNECTION_TIMEOUT=15000
DB_ENABLE_AUTO_SCALING=true
DB_ENABLE_CONNECTION_WARMUP=true
```

### AWS RDS Deployment
```env
# AWS RDS PostgreSQL
DATABASE_URL="postgresql://..."
DATABASE_TIER=premium
DB_MAX_CONNECTIONS=100
DB_CONNECTION_TIMEOUT=20000
DB_ENABLE_AUTO_SCALING=true
DB_ENABLE_CONNECTION_WARMUP=true
DB_ENABLE_LOAD_BALANCING=true
```

### Google Cloud SQL
```env
# Google Cloud SQL PostgreSQL
DATABASE_URL="postgresql://..."
DATABASE_TIER=premium
DB_MAX_CONNECTIONS=100
DB_CONNECTION_TIMEOUT=20000
DB_ENABLE_AUTO_SCALING=true
DB_ENABLE_CONNECTION_WARMUP=true
```

## Performance Tuning

### High-Traffic Configuration
For applications expecting high concurrent load:
```env
DATABASE_TIER=premium
DB_MIN_CONNECTIONS=20
DB_MAX_CONNECTIONS=150
DB_CONNECTION_TIMEOUT=25000
DB_IDLE_TIMEOUT=180000
DB_HEALTH_CHECK_INTERVAL=15000
DB_ENABLE_AUTO_SCALING=true
DB_SLOW_QUERY_THRESHOLD=500
DB_ENABLE_CONNECTION_WARMUP=true
DB_WARMUP_CONNECTIONS=10
```

### Low-Latency Configuration
For applications requiring minimal response times:
```env
DATABASE_TIER=enterprise
DB_MIN_CONNECTIONS=30
DB_MAX_CONNECTIONS=200
DB_CONNECTION_TIMEOUT=5000
DB_IDLE_TIMEOUT=60000
DB_HEALTH_CHECK_INTERVAL=10000
DB_ENABLE_AUTO_SCALING=true
DB_SLOW_QUERY_THRESHOLD=200
DB_ENABLE_CONNECTION_WARMUP=true
DB_WARMUP_CONNECTIONS=15
```

### Resource-Constrained Configuration
For applications with limited resources:
```env
DATABASE_TIER=basic
DB_MIN_CONNECTIONS=1
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=15000
DB_IDLE_TIMEOUT=45000
DB_HEALTH_CHECK_INTERVAL=60000
DB_ENABLE_AUTO_SCALING=false
DB_ENABLE_CONNECTION_WARMUP=false
```

## Monitoring and Alerting

### Metrics Collection
```env
DB_ENABLE_METRICS=true
DB_METRICS_RETENTION_HOURS=72  # 3 days
DATABASE_ENABLE_LOGGING=true
DATABASE_LOG_LEVEL=info
DATABASE_ENABLE_TRACING=true
```

### Health Checks
```env
DB_HEALTH_CHECK_INTERVAL=30000  # 30 seconds
DB_CONNECTION_LEAK_DETECTION=true
DB_CONNECTION_LEAK_TIMEOUT=300000  # 5 minutes
DB_SLOW_QUERY_THRESHOLD=1000  # 1 second
```

## Security Configuration

### Production Security
```env
# SSL/TLS Configuration
DATABASE_URL="postgresql://...?sslmode=require"

# Connection Security
DB_CONNECTION_TIMEOUT=10000  # Prevent hanging connections
DB_MAX_LIFETIME=3600000  # Rotate connections hourly
DB_CONNECTION_LEAK_DETECTION=true
```

## Load Testing Configuration

### Standard Load Test
```javascript
const loadTestConfig = {
  concurrentConnections: 20,
  testDurationMs: 120000,  // 2 minutes
  queryTypes: ['read', 'write', 'complex'],
  rampUpTimeMs: 15000,
  rampDownTimeMs: 15000,
  targetQPS: 50,
  enableMetrics: true
};
```

### Stress Test
```javascript
const stressTestConfig = {
  concurrentConnections: 100,
  testDurationMs: 300000,  // 5 minutes
  queryTypes: ['read', 'write', 'complex'],
  rampUpTimeMs: 30000,
  rampDownTimeMs: 30000,
  targetQPS: 200,
  enableMetrics: true
};
```

## Troubleshooting

### Common Issues and Solutions

#### High Connection Utilization
```env
# Increase pool size
DB_MAX_CONNECTIONS=50
# Enable auto-scaling
DB_ENABLE_AUTO_SCALING=true
# Reduce idle timeout
DB_IDLE_TIMEOUT=30000
```

#### Connection Timeouts
```env
# Increase timeout
DB_CONNECTION_TIMEOUT=20000
# Reduce max lifetime
DB_MAX_LIFETIME=1800000  # 30 minutes
# Enable connection warmup
DB_ENABLE_CONNECTION_WARMUP=true
```

#### Memory Issues
```env
# Reduce pool size
DB_MAX_CONNECTIONS=20
# Increase idle timeout to reuse connections
DB_IDLE_TIMEOUT=120000
# Disable auto-scaling
DB_ENABLE_AUTO_SCALING=false
```

#### Slow Queries
```env
# Lower threshold for detection
DB_SLOW_QUERY_THRESHOLD=500
# Enable detailed logging
DATABASE_LOG_LEVEL=query
# Enable metrics
DB_ENABLE_METRICS=true
```

## Best Practices

### Development Environment
- Use hobby or basic tier
- Disable auto-scaling
- Enable detailed logging
- Use shorter timeouts for faster feedback

### Staging Environment
- Mirror production tier but with reduced limits
- Enable all monitoring features
- Test auto-scaling behavior
- Validate configuration before production

### Production Environment
- Use appropriate tier for expected load
- Enable auto-scaling and monitoring
- Set up alerting for health checks
- Regular load testing
- Monitor and tune based on actual usage

## API Endpoints

### Monitoring
- `GET /api/admin/database/connection-pool?action=status`
- `GET /api/admin/database/connection-pool?action=metrics`
- `GET /api/admin/database/connection-pool?action=health`

### Management
- `POST /api/admin/database/connection-pool?action=run_load_test`
- `POST /api/admin/database/connection-pool?action=reset_metrics`
- `POST /api/admin/database/connection-pool?action=optimize_configuration`

### Testing
- `POST /api/admin/database/connection-pool?action=run_standard_tests`
- `POST /api/admin/database/connection-pool?action=simulate_load`

## Migration Guide

### From Basic to Production Configuration

1. **Backup current configuration**
2. **Update environment variables** based on tier
3. **Test with load testing** before full deployment
4. **Monitor metrics** after deployment
5. **Tune based on actual usage** patterns

### Upgrading Database Tiers

1. **Review current utilization** metrics
2. **Select appropriate tier** based on load
3. **Update configuration** gradually
4. **Monitor performance** improvements
5. **Adjust auto-scaling** thresholds if needed

This configuration guide ensures optimal database connection pool performance across different environments and use cases.
