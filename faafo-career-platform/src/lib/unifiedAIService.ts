import { GoogleGenerativeAI, GenerativeModel, GenerationConfig } from '@google/generative-ai';
import { cacheService } from './services/cacheService';

// Improved types
export interface AIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  cached?: boolean;
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
    model?: string;
    temperature?: number;
    generatedAt: string;
  };
}

export type AITaskType = 
  | 'resume_analysis'
  | 'career_recommendations'
  | 'skills_analysis'
  | 'interview_prep'
  | 'personality_analysis'
  | 'career_fit_analysis'
  | 'skill_gap_analysis'
  | 'learning_style_analysis'
  | 'market_trend_analysis'
  | 'personalized_content';

/**
 * Unified AI Service - Improved Gemini service with centralized configuration and better caching
 */
class UnifiedAIService {
  private static instance: UnifiedAIService;
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;
  private cacheEnabled: boolean;
  private cacheTTL: number;
  private modelName: string;

  private constructor() {
    const apiKey = process.env.GOOGLE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_GEMINI_API_KEY is required');
    }

    this.genAI = new GoogleGenerativeAI(apiKey);
    this.modelName = process.env.AI_MODEL || 'gemini-1.5-flash';
    this.model = this.genAI.getGenerativeModel({ model: this.modelName });
    this.cacheEnabled = process.env.AI_CACHE_ENABLED !== 'false';
    this.cacheTTL = parseInt(process.env.AI_CACHE_TTL || '3600');
  }

  static getInstance(): UnifiedAIService {
    if (!UnifiedAIService.instance) {
      UnifiedAIService.instance = new UnifiedAIService();
    }
    return UnifiedAIService.instance;
  }

  /**
   * Generate content with unified caching and error handling
   */
  private async generateContent(
    prompt: string,
    taskType: AITaskType,
    cacheKey?: string
  ): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // Check cache first
      if (this.cacheEnabled && cacheKey) {
        const cached = await this.getCachedResult(cacheKey);
        if (cached) {
          return { ...cached, cached: true };
        }
      }

      // Get task-specific configuration
      const config = this.getTaskConfig(taskType);

      // Generate content
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: config
      });

      const response = await result.response;
      const text = response.text();

      // Parse response
      let data;
      try {
        data = JSON.parse(text);
      } catch {
        data = { content: text };
      }

      const processingTime = Date.now() - startTime;
      const aiResponse: AIResponse = {
        success: true,
        data,
        cached: false,
        metadata: {
          tokensUsed: this.estimateTokens(prompt + text),
          processingTime,
          model: this.modelName,
          temperature: config.temperature,
          generatedAt: new Date().toISOString()
        }
      };

      // Cache the result
      if (this.cacheEnabled && cacheKey) {
        await this.cacheResult(cacheKey, aiResponse);
      }

      return aiResponse;

    } catch (error) {
      console.error('AI generation error:', error);
      
      return {
        success: false,
        error: this.handleError(error),
        metadata: {
          processingTime: Date.now() - startTime,
          generatedAt: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Analyze resume
   */
  async analyzeResume(resumeText: string, userId?: string): Promise<AIResponse> {
    const prompt = `
Analyze the following resume and provide a comprehensive assessment in JSON format:

${resumeText}

Please provide your analysis in the following JSON structure:
{
  "strengths": ["list of key strengths"],
  "weaknesses": ["areas for improvement"],
  "suggestions": ["specific improvement suggestions"],
  "skillsIdentified": ["list of skills found in resume"],
  "experienceLevel": "entry|mid|senior|executive",
  "industryFit": ["industries this person would fit well in"],
  "overallScore": 85
}

Focus on actionable insights and specific recommendations.
`;

    const cacheKey = userId ? `resume_analysis_${userId}_${this.hashContent(resumeText)}` : undefined;
    return this.generateContent(prompt, 'resume_analysis', cacheKey);
  }

  /**
   * Generate career recommendations
   */
  async generateCareerRecommendations(
    assessmentData: any,
    currentSkills: string[],
    preferences: Record<string, any>,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Based on the following information, provide personalized career recommendations:

Assessment Data: ${JSON.stringify(assessmentData)}
Current Skills: ${currentSkills.join(', ')}
Preferences: ${JSON.stringify(preferences)}

Please provide recommendations in the following JSON structure:
{
  "recommendations": [
    {
      "title": "Career Title",
      "description": "Detailed description",
      "matchScore": 95,
      "requiredSkills": ["skill1", "skill2"],
      "salaryRange": {"min": 50000, "max": 80000, "currency": "USD"},
      "growthPotential": 85,
      "reasoning": "Why this career fits"
    }
  ],
  "topMatch": "Best career match",
  "alternativePaths": ["alternative career paths"]
}

Provide 3-5 recommendations ranked by fit score.
`;

    const cacheKey = userId ? `career_recommendations_${userId}_${this.hashContent(JSON.stringify({ assessmentData, currentSkills, preferences }))}` : undefined;
    return this.generateContent(prompt, 'career_recommendations', cacheKey);
  }

  /**
   * Analyze skills gap
   */
  async analyzeSkillsGap(
    currentSkills: string[],
    targetCareerPath: string,
    experienceLevel: string,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Analyze the following skills for career development:

Current Skills: ${currentSkills.join(', ')}
Target Career: ${targetCareerPath}
Experience Level: ${experienceLevel}

Please provide analysis in the following JSON structure:
{
  "currentSkills": [
    {"name": "skill", "level": 8, "category": "technical"}
  ],
  "skillGaps": [
    {"skill": "missing skill", "importance": 9, "learningPath": ["step1", "step2"]}
  ],
  "recommendations": ["specific recommendations"],
  "prioritySkills": ["skills to focus on first"],
  "learningRecommendations": [
    {"skill": "skill name", "priority": "high|medium|low", "timeEstimate": "weeks"}
  ]
}

Focus on actionable learning paths and priority skills.
`;

    const cacheKey = userId ? `skills_analysis_${userId}_${this.hashContent(JSON.stringify({ currentSkills, targetCareerPath, experienceLevel }))}` : undefined;
    return this.generateContent(prompt, 'skills_analysis', cacheKey);
  }

  /**
   * Generate personalized content
   */
  async generatePersonalizedContent(
    taskType: AITaskType,
    context: Record<string, any>,
    metadata: Record<string, any>,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Task: ${taskType}
Context: ${JSON.stringify(context)}
Metadata: ${JSON.stringify(metadata)}

Please provide a comprehensive response based on the task type and context provided.
Return the response in JSON format when possible.
`;

    const cacheKey = userId ? `${taskType}_${userId}_${this.hashContent(JSON.stringify(context))}` : undefined;
    return this.generateContent(prompt, taskType, cacheKey);
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: 'Hello, respond with "OK"' }] }],
        generationConfig: { temperature: 0.1, maxOutputTokens: 10 }
      });

      const response = await result.response;
      const text = response.text().trim().toLowerCase();
      return text.includes('ok');
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Clear AI cache
   */
  async clearCache(): Promise<void> {
    try {
      await cacheService.clear();
      console.log('AI cache cleared successfully');
    } catch (error) {
      console.error('Failed to clear AI cache:', error);
      throw error;
    }
  }

  // Private helper methods
  private getTaskConfig(taskType: AITaskType): GenerationConfig {
    const configs: Record<AITaskType, GenerationConfig> = {
      resume_analysis: { temperature: 0.3, maxOutputTokens: 2048, topP: 0.95, topK: 40 },
      career_recommendations: { temperature: 0.7, maxOutputTokens: 1024, topP: 0.95, topK: 40 },
      skills_analysis: { temperature: 0.4, maxOutputTokens: 1536, topP: 0.95, topK: 40 },
      interview_prep: { temperature: 0.6, maxOutputTokens: 2048, topP: 0.95, topK: 40 },
      personality_analysis: { temperature: 0.5, maxOutputTokens: 1536, topP: 0.9 },
      career_fit_analysis: { temperature: 0.6, maxOutputTokens: 1024, topP: 0.95 },
      skill_gap_analysis: { temperature: 0.4, maxOutputTokens: 1536, topP: 0.95 },
      learning_style_analysis: { temperature: 0.5, maxOutputTokens: 1024, topP: 0.9 },
      market_trend_analysis: { temperature: 0.3, maxOutputTokens: 1536, topP: 0.95 },
      personalized_content: { temperature: 0.7, maxOutputTokens: 2048, topP: 0.95 }
    };

    return configs[taskType] || { temperature: 0.7, maxOutputTokens: 2048 };
  }

  private async getCachedResult(cacheKey: string): Promise<AIResponse | null> {
    try {
      return await cacheService.getJSON<AIResponse>(`ai:${cacheKey}`);
    } catch (error) {
      console.error('Cache retrieval error:', error);
      return null;
    }
  }

  private async cacheResult(cacheKey: string, result: AIResponse): Promise<void> {
    try {
      await cacheService.setJSON(`ai:${cacheKey}`, result, this.cacheTTL);
    } catch (error) {
      console.error('Cache storage error:', error);
    }
  }

  private handleError(error: unknown): string {
    if (error instanceof Error) {
      if (error.message.includes('quota')) return 'AI service quota exceeded. Please try again later.';
      if (error.message.includes('safety')) return 'Content blocked by AI safety filters.';
      if (error.message.includes('rate limit')) return 'AI service rate limit exceeded. Please try again later.';
      return error.message;
    }
    return 'AI service temporarily unavailable';
  }

  private estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }

  private hashContent(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }
}

// Export singleton instance
export const unifiedAIService = UnifiedAIService.getInstance();
