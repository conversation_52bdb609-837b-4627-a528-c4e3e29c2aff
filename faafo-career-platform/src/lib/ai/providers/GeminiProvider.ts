import { GoogleGenerativeAI, GenerativeModel, GenerationConfig } from '@google/generative-ai';
import { AIProvider } from '../interfaces/AIProvider';
import {
  AIRequest,
  AIResponse,
  AIProviderConfig,
  AIProviderCapabilities,
  AITaskType,
  AIProviderError
} from '../types';
import { aiConfig } from '../config/AIConfig';

/**
 * Google Gemini AI Provider implementation
 */
export class Gemini<PERSON><PERSON><PERSON> extends AIProvider {
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;

  constructor(config: AIProviderConfig) {
    super(config);
    
    if (!config.apiKey) {
      throw new AIProviderError('Gemini API key is required', 'gemini');
    }

    this.genAI = new GoogleGenerativeAI(config.apiKey);
    this.model = this.genAI.getGenerativeModel({ model: config.model });
  }

  getProviderName(): string {
    return 'gemini';
  }

  getCapabilities(): AIProviderCapabilities {
    return {
      supportedTasks: [
        'resume_analysis',
        'career_recommendations',
        'skills_analysis',
        'interview_prep',
        'personality_analysis',
        'career_fit_analysis',
        'skill_gap_analysis',
        'learning_style_analysis',
        'market_trend_analysis',
        'personalized_content'
      ],
      maxTokens: 8192,
      supportsStreaming: true,
      supportsImages: true,
      supportsFiles: false
    };
  }

  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: 'Hello, respond with "OK"' }] }],
        generationConfig: {
          temperature: 0.1,
          maxOutputTokens: 10
        }
      });

      const response = await result.response;
      const text = response.text().trim().toLowerCase();
      return text.includes('ok');
    } catch (error) {
      console.error('Gemini health check failed:', error);
      return false;
    }
  }

  async generateContent(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // Validate request
      if (!request.prompt) {
        throw new AIProviderError('Prompt is required', 'gemini');
      }

      if (!this.supportsTask(request.taskType)) {
        throw new AIProviderError(`Task type ${request.taskType} is not supported`, 'gemini');
      }

      // Get task-specific configuration
      const taskConfig = aiConfig.getTaskConfig(request.taskType);
      const generationConfig = this.buildGenerationConfig(request.taskType, taskConfig);

      // Generate content
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: request.prompt }] }],
        generationConfig
      });

      const response = await result.response;
      const text = response.text();

      // Parse response
      let data;
      try {
        data = JSON.parse(text);
      } catch {
        // If JSON parsing fails, return as plain text
        data = { content: text };
      }

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        data,
        cached: false,
        provider: this.getProviderName(),
        metadata: {
          tokensUsed: this.estimateTokens(request.prompt + text),
          processingTime,
          model: this.config.model,
          temperature: generationConfig.temperature,
          generatedAt: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Gemini generation error:', error);
      
      if (error instanceof AIProviderError) {
        throw error;
      }

      // Handle specific Gemini errors
      if (error instanceof Error) {
        if (error.message.includes('quota')) {
          throw new AIProviderError(
            'Gemini API quota exceeded',
            'gemini',
            'QUOTA_EXCEEDED',
            429
          );
        }
        
        if (error.message.includes('safety')) {
          throw new AIProviderError(
            'Content blocked by Gemini safety filters',
            'gemini',
            'SAFETY_FILTER',
            400
          );
        }

        if (error.message.includes('rate limit')) {
          throw new AIProviderError(
            'Gemini API rate limit exceeded',
            'gemini',
            'RATE_LIMIT',
            429
          );
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Gemini AI service unavailable',
        provider: this.getProviderName(),
        metadata: {
          processingTime: Date.now() - startTime,
          generatedAt: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Build Gemini-specific generation configuration
   */
  private buildGenerationConfig(taskType: AITaskType, taskConfig?: any): GenerationConfig {
    const baseConfig: GenerationConfig = {
      temperature: taskConfig?.temperature ?? this.config.temperature,
      maxOutputTokens: Math.min(taskConfig?.maxTokens ?? this.config.maxTokens, 8192),
      topP: taskConfig?.topP ?? 0.95,
      topK: taskConfig?.topK ?? 40
    };

    // Task-specific adjustments
    switch (taskType) {
      case 'resume_analysis':
        return {
          ...baseConfig,
          temperature: 0.3,
          maxOutputTokens: 2048
        };
      
      case 'career_recommendations':
        return {
          ...baseConfig,
          temperature: 0.7,
          maxOutputTokens: 1024
        };
      
      case 'skills_analysis':
        return {
          ...baseConfig,
          temperature: 0.4,
          maxOutputTokens: 1536
        };
      
      case 'interview_prep':
        return {
          ...baseConfig,
          temperature: 0.6,
          maxOutputTokens: 2048
        };
      
      default:
        return baseConfig;
    }
  }

  /**
   * Estimate token count (rough approximation)
   */
  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Enhanced resume analysis with Gemini-specific optimizations
   */
  protected buildResumeAnalysisPrompt(resumeText: string): string {
    return `
You are an expert career counselor and resume analyst. Analyze the following resume and provide a comprehensive assessment.

RESUME CONTENT:
${resumeText}

ANALYSIS REQUIREMENTS:
1. Identify key strengths and areas for improvement
2. Extract and categorize all skills mentioned
3. Assess experience level and industry fit
4. Provide specific, actionable suggestions
5. Give an overall score (0-100)

RESPONSE FORMAT (JSON):
{
  "strengths": ["specific strength 1", "specific strength 2", ...],
  "weaknesses": ["area for improvement 1", "area for improvement 2", ...],
  "suggestions": ["actionable suggestion 1", "actionable suggestion 2", ...],
  "skillsIdentified": ["skill1", "skill2", "skill3", ...],
  "experienceLevel": "entry|mid|senior|executive",
  "industryFit": ["industry1", "industry2", ...],
  "overallScore": 85
}

Focus on providing specific, actionable insights that will help improve the resume's effectiveness.
`;
  }

  /**
   * Enhanced career recommendations with Gemini-specific optimizations
   */
  protected buildCareerRecommendationsPrompt(
    assessmentData: any,
    currentSkills: string[],
    preferences: Record<string, any>
  ): string {
    return `
You are an expert career counselor. Based on the provided information, generate personalized career recommendations.

ASSESSMENT DATA:
${JSON.stringify(assessmentData, null, 2)}

CURRENT SKILLS:
${currentSkills.join(', ')}

PREFERENCES:
${JSON.stringify(preferences, null, 2)}

ANALYSIS REQUIREMENTS:
1. Analyze personality traits and work style preferences
2. Match skills to career opportunities
3. Consider growth potential and market demand
4. Provide salary insights where possible
5. Rank recommendations by fit score

RESPONSE FORMAT (JSON):
{
  "recommendations": [
    {
      "title": "Software Engineer",
      "description": "Detailed role description and why it fits",
      "matchScore": 95,
      "requiredSkills": ["JavaScript", "React", "Node.js"],
      "salaryRange": {"min": 70000, "max": 120000, "currency": "USD"},
      "growthPotential": 90,
      "reasoning": "Specific reasons why this career matches the person's profile"
    }
  ],
  "topMatch": "Software Engineer",
  "alternativePaths": ["Data Analyst", "Product Manager", "UX Designer"]
}

Provide 3-5 high-quality recommendations ranked by match score (0-100).
`;
  }

  /**
   * Enhanced skills analysis with Gemini-specific optimizations
   */
  protected buildSkillsAnalysisPrompt(currentSkills: string[], targetRole?: string): string {
    const targetRoleSection = targetRole ? `\nTARGET ROLE: ${targetRole}` : '';
    
    return `
You are an expert skills analyst and career development specialist. Analyze the provided skills and create a comprehensive development plan.

CURRENT SKILLS:
${currentSkills.join(', ')}${targetRoleSection}

ANALYSIS REQUIREMENTS:
1. Categorize and assess current skill levels
2. Identify critical skill gaps for career advancement
3. Prioritize skills by importance and market demand
4. Provide specific learning paths and resources
5. Consider both technical and soft skills

RESPONSE FORMAT (JSON):
{
  "currentSkills": [
    {"name": "JavaScript", "level": 8, "category": "technical"},
    {"name": "Communication", "level": 7, "category": "soft"}
  ],
  "skillGaps": [
    {
      "skill": "React",
      "importance": 9,
      "learningPath": ["Complete React basics course", "Build 3 projects", "Learn advanced patterns"]
    }
  ],
  "recommendations": ["Focus on modern frameworks", "Develop leadership skills"],
  "prioritySkills": ["React", "TypeScript", "System Design"]
}

Skill levels: 1-10 scale (1=beginner, 10=expert)
Importance: 1-10 scale (1=nice to have, 10=critical)
`;
  }
}
