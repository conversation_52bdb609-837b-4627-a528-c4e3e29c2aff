import OpenAI from 'openai';
import { AIProvider } from '../interfaces/AIProvider';
import {
  AIRequest,
  AIResponse,
  AIProviderConfig,
  AIProviderCapabilities,
  AITaskType,
  AIProviderError,
  AIRateLimitError
} from '../types';
import { aiConfig } from '../config/AIConfig';

/**
 * OpenAI Provider implementation
 */
export class OpenAIProvider extends AIProvider {
  private client: OpenAI;

  constructor(config: AIProviderConfig) {
    super(config);
    
    if (!config.apiKey) {
      throw new AIProviderError('OpenAI API key is required', 'openai');
    }

    this.client = new OpenAI({
      apiKey: config.apiKey
    });
  }

  getProviderName(): string {
    return 'openai';
  }

  getCapabilities(): AIProviderCapabilities {
    return {
      supportedTasks: [
        'resume_analysis',
        'career_recommendations',
        'skills_analysis',
        'interview_prep',
        'personality_analysis',
        'career_fit_analysis',
        'skill_gap_analysis',
        'learning_style_analysis',
        'market_trend_analysis',
        'personalized_content'
      ],
      maxTokens: 4096,
      supportsStreaming: true,
      supportsImages: true,
      supportsFiles: false
    };
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.chat.completions.create({
        model: this.config.model,
        messages: [
          { role: 'user', content: 'Hello, respond with "OK"' }
        ],
        max_tokens: 10,
        temperature: 0.1
      });

      const text = response.choices[0]?.message?.content?.trim().toLowerCase();
      return text?.includes('ok') ?? false;
    } catch (error) {
      console.error('OpenAI health check failed:', error);
      return false;
    }
  }

  async generateContent(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // Validate request
      if (!request.prompt) {
        throw new AIProviderError('Prompt is required', 'openai');
      }

      if (!this.supportsTask(request.taskType)) {
        throw new AIProviderError(`Task type ${request.taskType} is not supported`, 'openai');
      }

      // Get task-specific configuration
      const taskConfig = aiConfig.getTaskConfig(request.taskType);
      const chatConfig = this.buildChatConfig(request.taskType, taskConfig);

      // Generate content
      const response = await this.client.chat.completions.create({
        model: this.config.model,
        messages: [
          { role: 'system', content: this.getSystemPrompt(request.taskType) },
          { role: 'user', content: request.prompt }
        ],
        ...chatConfig
      });

      const text = response.choices[0]?.message?.content || '';
      
      // Parse response
      let data;
      try {
        data = JSON.parse(text);
      } catch {
        // If JSON parsing fails, return as plain text
        data = { content: text };
      }

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        data,
        cached: false,
        provider: this.getProviderName(),
        metadata: {
          tokensUsed: response.usage?.total_tokens || 0,
          processingTime,
          model: this.config.model,
          temperature: chatConfig.temperature,
          generatedAt: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('OpenAI generation error:', error);
      
      if (error instanceof AIProviderError) {
        throw error;
      }

      // Handle specific OpenAI errors
      if (error instanceof Error) {
        if (error.message.includes('rate_limit_exceeded')) {
          throw new AIRateLimitError(
            'OpenAI API rate limit exceeded',
            'openai'
          );
        }
        
        if (error.message.includes('insufficient_quota')) {
          throw new AIProviderError(
            'OpenAI API quota exceeded',
            'openai',
            'QUOTA_EXCEEDED',
            429
          );
        }

        if (error.message.includes('content_filter')) {
          throw new AIProviderError(
            'Content blocked by OpenAI content filter',
            'openai',
            'CONTENT_FILTER',
            400
          );
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'OpenAI service unavailable',
        provider: this.getProviderName(),
        metadata: {
          processingTime: Date.now() - startTime,
          generatedAt: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Build OpenAI-specific chat configuration
   */
  private buildChatConfig(taskType: AITaskType, taskConfig?: any): Partial<OpenAI.Chat.Completions.ChatCompletionCreateParams> {
    const baseConfig = {
      temperature: taskConfig?.temperature ?? this.config.temperature,
      max_tokens: Math.min(taskConfig?.maxTokens ?? this.config.maxTokens, 4096),
      top_p: taskConfig?.topP ?? 1,
      frequency_penalty: taskConfig?.frequencyPenalty ?? 0,
      presence_penalty: taskConfig?.presencePenalty ?? 0
    };

    // Task-specific adjustments
    switch (taskType) {
      case 'resume_analysis':
        return {
          ...baseConfig,
          temperature: 0.3,
          max_tokens: 2048
        };
      
      case 'career_recommendations':
        return {
          ...baseConfig,
          temperature: 0.7,
          max_tokens: 1024
        };
      
      case 'skills_analysis':
        return {
          ...baseConfig,
          temperature: 0.4,
          max_tokens: 1536
        };
      
      case 'interview_prep':
        return {
          ...baseConfig,
          temperature: 0.6,
          max_tokens: 2048
        };
      
      default:
        return baseConfig;
    }
  }

  /**
   * Get system prompt for different task types
   */
  private getSystemPrompt(taskType: AITaskType): string {
    switch (taskType) {
      case 'resume_analysis':
        return 'You are an expert career counselor and resume analyst. Provide comprehensive, actionable resume analysis in JSON format.';
      
      case 'career_recommendations':
        return 'You are an expert career counselor. Provide personalized career recommendations based on assessment data, skills, and preferences in JSON format.';
      
      case 'skills_analysis':
        return 'You are an expert skills analyst and career development specialist. Analyze skills and provide development recommendations in JSON format.';
      
      case 'interview_prep':
        return 'You are an expert interview coach. Provide comprehensive interview preparation materials and advice in JSON format.';
      
      case 'personality_analysis':
        return 'You are an expert personality analyst. Provide detailed personality analysis for career development in JSON format.';
      
      default:
        return 'You are an expert AI assistant. Provide helpful, accurate, and well-structured responses in JSON format when possible.';
    }
  }

  /**
   * Enhanced resume analysis with OpenAI-specific optimizations
   */
  protected buildResumeAnalysisPrompt(resumeText: string): string {
    return `
Analyze the following resume and provide a comprehensive assessment in JSON format.

RESUME CONTENT:
${resumeText}

Please provide your analysis in the following JSON structure:
{
  "strengths": ["list of key strengths"],
  "weaknesses": ["areas for improvement"],
  "suggestions": ["specific improvement suggestions"],
  "skillsIdentified": ["list of skills found in resume"],
  "experienceLevel": "entry|mid|senior|executive",
  "industryFit": ["industries this person would fit well in"],
  "overallScore": 85
}

Focus on:
- Specific, actionable feedback
- Industry-relevant insights
- Skills assessment and gaps
- Professional presentation quality
- Achievement quantification
`;
  }

  /**
   * Enhanced career recommendations with OpenAI-specific optimizations
   */
  protected buildCareerRecommendationsPrompt(
    assessmentData: any,
    currentSkills: string[],
    preferences: Record<string, any>
  ): string {
    return `
Based on the following information, provide personalized career recommendations in JSON format.

ASSESSMENT DATA:
${JSON.stringify(assessmentData, null, 2)}

CURRENT SKILLS:
${currentSkills.join(', ')}

PREFERENCES:
${JSON.stringify(preferences, null, 2)}

Please provide recommendations in the following JSON structure:
{
  "recommendations": [
    {
      "title": "Career Title",
      "description": "Detailed description",
      "matchScore": 95,
      "requiredSkills": ["skill1", "skill2"],
      "salaryRange": {"min": 50000, "max": 80000, "currency": "USD"},
      "growthPotential": 85,
      "reasoning": "Why this career fits"
    }
  ],
  "topMatch": "Best career match",
  "alternativePaths": ["alternative career paths"]
}

Consider:
- Personality traits and work style
- Current skills and experience
- Market demand and growth potential
- Salary expectations and location preferences
- Career progression opportunities
`;
  }

  /**
   * Enhanced skills analysis with OpenAI-specific optimizations
   */
  protected buildSkillsAnalysisPrompt(currentSkills: string[], targetRole?: string): string {
    const targetRoleText = targetRole ? `Target Role: ${targetRole}` : '';
    
    return `
Analyze the following skills and provide comprehensive recommendations in JSON format.

Current Skills: ${currentSkills.join(', ')}
${targetRoleText}

Please provide analysis in the following JSON structure:
{
  "currentSkills": [
    {"name": "skill", "level": 8, "category": "technical"}
  ],
  "skillGaps": [
    {"skill": "missing skill", "importance": 9, "learningPath": ["step1", "step2"]}
  ],
  "recommendations": ["specific recommendations"],
  "prioritySkills": ["skills to focus on first"]
}

Consider:
- Current market demand for skills
- Skill transferability across roles
- Learning difficulty and time investment
- Industry-specific requirements
- Future skill trends and emerging technologies
`;
  }
}
