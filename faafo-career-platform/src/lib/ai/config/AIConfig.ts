import { <PERSON><PERSON><PERSON>ider, AIProviderConfig, AITaskConfig, CacheConfig } from '../types';

/**
 * Centralized AI configuration management
 */
export class AIConfig {
  private static instance: AIConfig;
  private configs: Map<AIProvider, AIProviderConfig> = new Map();
  private taskConfigs: Map<string, AITaskConfig> = new Map();
  private cacheConfig: CacheConfig;
  private defaultProvider: AIProvider;

  private constructor() {
    this.loadConfiguration();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): AIConfig {
    if (!AIConfig.instance) {
      AIConfig.instance = new AIConfig();
    }
    return AIConfig.instance;
  }

  /**
   * Load configuration from environment variables
   */
  private loadConfiguration(): void {
    // Load provider configurations
    this.loadProviderConfigs();
    
    // Load task-specific configurations
    this.loadTaskConfigs();
    
    // Load cache configuration
    this.loadCacheConfig();
    
    // Determine default provider
    this.determineDefaultProvider();
  }

  /**
   * Load provider configurations from environment
   */
  private loadProviderConfigs(): void {
    // Gemini configuration (primary and only provider for now)
    if (process.env.GOOGLE_GEMINI_API_KEY) {
      this.configs.set('gemini', {
        apiKey: process.env.GOOGLE_GEMINI_API_KEY,
        model: process.env.GEMINI_MODEL || 'gemini-1.5-flash',
        maxTokens: parseInt(process.env.GEMINI_MAX_TOKENS || '2048'),
        temperature: parseFloat(process.env.GEMINI_TEMPERATURE || '0.7'),
        enabled: process.env.GEMINI_ENABLED !== 'false',
        rateLimits: {
          requestsPerMinute: parseInt(process.env.GEMINI_REQUESTS_PER_MINUTE || '60'),
          tokensPerMinute: parseInt(process.env.GEMINI_TOKENS_PER_MINUTE || '100000')
        }
      });
    }
  }

  /**
   * Load task-specific configurations
   */
  private loadTaskConfigs(): void {
    // Resume analysis
    this.taskConfigs.set('resume_analysis', {
      temperature: 0.3,
      maxTokens: 2048,
      topP: 0.95,
      topK: 40
    });

    // Career recommendations
    this.taskConfigs.set('career_recommendations', {
      temperature: 0.7,
      maxTokens: 1024,
      topP: 0.95,
      topK: 40
    });

    // Skills analysis
    this.taskConfigs.set('skills_analysis', {
      temperature: 0.4,
      maxTokens: 1536,
      topP: 0.95,
      topK: 40
    });

    // Interview preparation
    this.taskConfigs.set('interview_prep', {
      temperature: 0.6,
      maxTokens: 2048,
      topP: 0.95,
      topK: 40
    });

    // Personality analysis
    this.taskConfigs.set('personality_analysis', {
      temperature: 0.5,
      maxTokens: 1536,
      topP: 0.9
    });

    // Career fit analysis
    this.taskConfigs.set('career_fit_analysis', {
      temperature: 0.6,
      maxTokens: 1024,
      topP: 0.95
    });

    // Skill gap analysis
    this.taskConfigs.set('skill_gap_analysis', {
      temperature: 0.4,
      maxTokens: 1536,
      topP: 0.95
    });

    // Learning style analysis
    this.taskConfigs.set('learning_style_analysis', {
      temperature: 0.5,
      maxTokens: 1024,
      topP: 0.9
    });

    // Market trend analysis
    this.taskConfigs.set('market_trend_analysis', {
      temperature: 0.3,
      maxTokens: 1536,
      topP: 0.95
    });

    // Personalized content
    this.taskConfigs.set('personalized_content', {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 0.95
    });
  }

  /**
   * Load cache configuration
   */
  private loadCacheConfig(): void {
    this.cacheConfig = {
      enabled: process.env.AI_CACHE_ENABLED !== 'false',
      ttl: parseInt(process.env.AI_CACHE_TTL || '3600'),
      keyPrefix: process.env.AI_CACHE_PREFIX || 'ai:'
    };
  }

  /**
   * Determine default provider based on configuration and preferences
   */
  private determineDefaultProvider(): void {
    // Check environment variable first
    const envProvider = process.env.AI_PROVIDER as AIProvider;
    if (envProvider && this.configs.has(envProvider) && this.configs.get(envProvider)?.enabled) {
      this.defaultProvider = envProvider;
      return;
    }

    // Fallback to first available provider
    const availableProviders = Array.from(this.configs.entries())
      .filter(([_, config]) => config.enabled)
      .map(([provider]) => provider);

    if (availableProviders.length > 0) {
      // Prefer Gemini, then OpenAI, then Anthropic
      const preferenceOrder: AIProvider[] = ['gemini', 'openai', 'anthropic'];
      for (const preferred of preferenceOrder) {
        if (availableProviders.includes(preferred)) {
          this.defaultProvider = preferred;
          return;
        }
      }
      this.defaultProvider = availableProviders[0];
    } else {
      throw new Error('No AI providers configured. Please set up at least one AI provider.');
    }
  }

  /**
   * Get provider configuration
   */
  getProviderConfig(provider: AIProvider): AIProviderConfig | undefined {
    return this.configs.get(provider);
  }

  /**
   * Get all available providers
   */
  getAvailableProviders(): AIProvider[] {
    return Array.from(this.configs.entries())
      .filter(([_, config]) => config.enabled)
      .map(([provider]) => provider);
  }

  /**
   * Get default provider
   */
  getDefaultProvider(): AIProvider {
    return this.defaultProvider;
  }

  /**
   * Get task configuration
   */
  getTaskConfig(taskType: string): AITaskConfig | undefined {
    return this.taskConfigs.get(taskType);
  }

  /**
   * Get cache configuration
   */
  getCacheConfig(): CacheConfig {
    return this.cacheConfig;
  }

  /**
   * Check if provider is available and enabled
   */
  isProviderAvailable(provider: AIProvider): boolean {
    const config = this.configs.get(provider);
    return !!(config && config.enabled && config.apiKey);
  }

  /**
   * Get provider with fallback
   */
  getProviderWithFallback(preferredProvider?: AIProvider): AIProvider {
    if (preferredProvider && this.isProviderAvailable(preferredProvider)) {
      return preferredProvider;
    }
    return this.defaultProvider;
  }

  /**
   * Reload configuration (useful for testing or dynamic updates)
   */
  reload(): void {
    this.configs.clear();
    this.taskConfigs.clear();
    this.loadConfiguration();
  }

  /**
   * Validate all configurations
   */
  validateConfigurations(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if at least one provider is configured
    if (this.configs.size === 0) {
      errors.push('No AI providers configured');
    }

    // Validate each provider configuration
    for (const [provider, config] of this.configs.entries()) {
      if (!config.apiKey) {
        errors.push(`${provider}: API key is required`);
      }
      if (!config.model) {
        errors.push(`${provider}: Model is required`);
      }
      if (config.maxTokens <= 0) {
        errors.push(`${provider}: Max tokens must be greater than 0`);
      }
      if (config.temperature < 0 || config.temperature > 2) {
        errors.push(`${provider}: Temperature must be between 0 and 2`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const aiConfig = AIConfig.getInstance();
