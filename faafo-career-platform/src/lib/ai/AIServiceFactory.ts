import { AIProvider as AIProviderType, AIProviderConfig, AIConfigurationError } from './types';
import { AIProvider } from './interfaces/AIProvider';
import { GeminiProvider } from './providers/GeminiProvider';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { AnthropicProvider } from './providers/AnthropicProvider';
import { aiConfig } from './config/AIConfig';

/**
 * Factory class for creating AI provider instances
 */
export class AIServiceFactory {
  private static instance: AIServiceFactory;
  private providers: Map<AIProviderType, AIProvider> = new Map();

  private constructor() {
    this.initializeProviders();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): AIServiceFactory {
    if (!AIServiceFactory.instance) {
      AIServiceFactory.instance = new AIServiceFactory();
    }
    return AIServiceFactory.instance;
  }

  /**
   * Initialize all available providers
   */
  private initializeProviders(): void {
    const availableProviders = aiConfig.getAvailableProviders();

    for (const providerType of availableProviders) {
      try {
        const config = aiConfig.getProviderConfig(providerType);
        if (!config) {
          console.warn(`Configuration not found for provider: ${providerType}`);
          continue;
        }

        const provider = this.createProvider(providerType, config);
        if (provider.validateConfig()) {
          this.providers.set(providerType, provider);
          console.log(`Initialized AI provider: ${providerType}`);
        } else {
          console.warn(`Invalid configuration for provider: ${providerType}`);
        }
      } catch (error) {
        console.error(`Failed to initialize provider ${providerType}:`, error);
      }
    }

    if (this.providers.size === 0) {
      throw new AIConfigurationError('No AI providers could be initialized');
    }
  }

  /**
   * Create a provider instance based on type
   */
  private createProvider(type: AIProviderType, config: AIProviderConfig): AIProvider {
    switch (type) {
      case 'gemini':
        return new GeminiProvider(config);
      
      case 'openai':
        return new OpenAIProvider(config);
      
      case 'anthropic':
        return new AnthropicProvider(config);
      
      default:
        throw new AIConfigurationError(`Unsupported provider type: ${type}`);
    }
  }

  /**
   * Get a provider instance
   */
  getProvider(type?: AIProviderType): AIProvider {
    // If no type specified, use default provider
    if (!type) {
      type = aiConfig.getDefaultProvider();
    }

    // Check if provider is available
    const provider = this.providers.get(type);
    if (!provider) {
      // Try to get fallback provider
      const fallbackType = this.getFallbackProvider(type);
      const fallbackProvider = this.providers.get(fallbackType);
      
      if (!fallbackProvider) {
        throw new AIConfigurationError(`No available AI providers. Requested: ${type}`);
      }

      console.warn(`Provider ${type} not available, using fallback: ${fallbackType}`);
      return fallbackProvider;
    }

    return provider;
  }

  /**
   * Get provider with health check
   */
  async getHealthyProvider(preferredType?: AIProviderType): Promise<AIProvider> {
    const providers = preferredType 
      ? [preferredType, ...this.getAvailableProviders().filter(p => p !== preferredType)]
      : this.getAvailableProviders();

    for (const providerType of providers) {
      const provider = this.providers.get(providerType);
      if (provider) {
        try {
          const isHealthy = await provider.healthCheck();
          if (isHealthy) {
            return provider;
          }
          console.warn(`Provider ${providerType} failed health check`);
        } catch (error) {
          console.error(`Health check error for ${providerType}:`, error);
        }
      }
    }

    throw new AIConfigurationError('No healthy AI providers available');
  }

  /**
   * Get fallback provider
   */
  private getFallbackProvider(requestedType: AIProviderType): AIProviderType {
    const available = this.getAvailableProviders();
    
    // Remove the requested type from available options
    const fallbackOptions = available.filter(p => p !== requestedType);
    
    if (fallbackOptions.length === 0) {
      throw new AIConfigurationError('No fallback providers available');
    }

    // Prefer providers in this order: gemini, openai, anthropic
    const preferenceOrder: AIProviderType[] = ['gemini', 'openai', 'anthropic'];
    
    for (const preferred of preferenceOrder) {
      if (fallbackOptions.includes(preferred)) {
        return preferred;
      }
    }

    // Return first available if no preferred option found
    return fallbackOptions[0];
  }

  /**
   * Get all available provider types
   */
  getAvailableProviders(): AIProviderType[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Check if a provider is available
   */
  isProviderAvailable(type: AIProviderType): boolean {
    return this.providers.has(type);
  }

  /**
   * Get provider capabilities
   */
  getProviderCapabilities(type: AIProviderType) {
    const provider = this.providers.get(type);
    return provider?.getCapabilities();
  }

  /**
   * Get best provider for a specific task
   */
  getBestProviderForTask(taskType: string, preferredProvider?: AIProviderType): AIProvider {
    // If preferred provider is specified and supports the task, use it
    if (preferredProvider) {
      const provider = this.providers.get(preferredProvider);
      if (provider && provider.supportsTask(taskType as any)) {
        return provider;
      }
    }

    // Find providers that support the task
    const supportingProviders = Array.from(this.providers.entries())
      .filter(([_, provider]) => provider.supportsTask(taskType as any))
      .map(([type, provider]) => ({ type, provider }));

    if (supportingProviders.length === 0) {
      throw new AIConfigurationError(`No providers support task type: ${taskType}`);
    }

    // Return the first supporting provider (could be enhanced with more sophisticated selection)
    return supportingProviders[0].provider;
  }

  /**
   * Reload all providers (useful for configuration updates)
   */
  reload(): void {
    this.providers.clear();
    aiConfig.reload();
    this.initializeProviders();
  }

  /**
   * Get provider statistics
   */
  getProviderStats() {
    const stats = {
      total: this.providers.size,
      available: this.getAvailableProviders(),
      default: aiConfig.getDefaultProvider(),
      capabilities: {} as Record<AIProviderType, any>
    };

    for (const [type, provider] of this.providers.entries()) {
      stats.capabilities[type] = provider.getCapabilities();
    }

    return stats;
  }

  /**
   * Test all providers
   */
  async testAllProviders(): Promise<Record<AIProviderType, boolean>> {
    const results: Record<string, boolean> = {};

    const testPromises = Array.from(this.providers.entries()).map(async ([type, provider]) => {
      try {
        const isHealthy = await provider.healthCheck();
        results[type] = isHealthy;
      } catch (error) {
        console.error(`Test failed for provider ${type}:`, error);
        results[type] = false;
      }
    });

    await Promise.all(testPromises);
    return results as Record<AIProviderType, boolean>;
  }

  /**
   * Get provider load balancing recommendation
   */
  getLoadBalancedProvider(): AIProvider {
    const available = this.getAvailableProviders();
    
    if (available.length === 0) {
      throw new AIConfigurationError('No providers available for load balancing');
    }

    // Simple round-robin for now (could be enhanced with actual load metrics)
    const index = Math.floor(Math.random() * available.length);
    return this.providers.get(available[index])!;
  }
}

// Export singleton instance
export const aiServiceFactory = AIServiceFactory.getInstance();
