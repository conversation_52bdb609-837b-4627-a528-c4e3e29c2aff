// Core AI service types and interfaces

export interface AIRequest {
  prompt: string;
  context?: Record<string, any>;
  userId?: string;
  cacheKey?: string;
  taskType: AITaskType;
}

export interface AIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  cached?: boolean;
  provider?: string;
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
    model?: string;
    temperature?: number;
    generatedAt: string;
  };
}

export interface AIProviderConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
  enabled: boolean;
  rateLimits?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

export interface CacheConfig {
  enabled: boolean;
  ttl: number;
  keyPrefix: string;
}

export type AITaskType = 
  | 'resume_analysis'
  | 'career_recommendations'
  | 'skills_analysis'
  | 'interview_prep'
  | 'personality_analysis'
  | 'career_fit_analysis'
  | 'skill_gap_analysis'
  | 'learning_style_analysis'
  | 'market_trend_analysis'
  | 'personalized_content';

export type AIProvider = 'gemini' | 'openai' | 'anthropic';

export interface AITaskConfig {
  temperature: number;
  maxTokens: number;
  topP?: number;
  topK?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

export interface AIProviderCapabilities {
  supportedTasks: AITaskType[];
  maxTokens: number;
  supportsStreaming: boolean;
  supportsImages: boolean;
  supportsFiles: boolean;
}

// Specific response types for different AI tasks
export interface ResumeAnalysisResponse {
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  skillsIdentified: string[];
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  industryFit: string[];
  overallScore: number;
}

export interface CareerRecommendationResponse {
  recommendations: Array<{
    title: string;
    description: string;
    matchScore: number;
    requiredSkills: string[];
    salaryRange?: {
      min: number;
      max: number;
      currency: string;
    };
    growthPotential: number;
    reasoning: string;
  }>;
  topMatch: string;
  alternativePaths: string[];
}

export interface SkillsAnalysisResponse {
  currentSkills: Array<{
    name: string;
    level: number;
    category: string;
  }>;
  skillGaps: Array<{
    skill: string;
    importance: number;
    learningPath: string[];
  }>;
  recommendations: string[];
  prioritySkills: string[];
}

// Error types
export class AIProviderError extends Error {
  constructor(
    message: string,
    public provider: AIProvider,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'AIProviderError';
  }
}

export class AIConfigurationError extends Error {
  constructor(message: string, public provider?: AIProvider) {
    super(message);
    this.name = 'AIConfigurationError';
  }
}

export class AIRateLimitError extends Error {
  constructor(
    message: string,
    public provider: AIProvider,
    public retryAfter?: number
  ) {
    super(message);
    this.name = 'AIRateLimitError';
  }
}
