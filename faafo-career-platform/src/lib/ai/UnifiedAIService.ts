import { GoogleGenerativeA<PERSON>, GenerativeModel, GenerationConfig } from '@google/generative-ai';
import { cacheService } from '../services/cacheService';

// Simplified types for single provider
export interface AIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  cached?: boolean;
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
    model?: string;
    temperature?: number;
    generatedAt: string;
  };
}

export type AITaskType = 
  | 'resume_analysis'
  | 'career_recommendations'
  | 'skills_analysis'
  | 'interview_prep'
  | 'personality_analysis'
  | 'career_fit_analysis'
  | 'skill_gap_analysis'
  | 'learning_style_analysis'
  | 'market_trend_analysis'
  | 'personalized_content';

export interface AIConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
  cacheEnabled: boolean;
  cacheTTL: number;
}

export interface TaskConfig {
  temperature: number;
  maxTokens: number;
  topP?: number;
  topK?: number;
}

/**
 * Unified AI Service - Centralized configuration and improved caching for Gemini
 */
export class UnifiedAIService {
  private static instance: UnifiedAIService;
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;
  private config: AIConfig;
  private taskConfigs: Map<AITaskType, TaskConfig> = new Map();

  private constructor() {
    this.loadConfiguration();
    this.genAI = new GoogleGenerativeAI(this.config.apiKey);
    this.model = this.genAI.getGenerativeModel({ model: this.config.model });
    this.initializeTaskConfigs();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): UnifiedAIService {
    if (!UnifiedAIService.instance) {
      UnifiedAIService.instance = new UnifiedAIService();
    }
    return UnifiedAIService.instance;
  }

  /**
   * Load centralized configuration
   */
  private loadConfiguration(): void {
    this.config = {
      apiKey: process.env.GOOGLE_GEMINI_API_KEY || '',
      model: process.env.AI_MODEL || 'gemini-1.5-flash',
      maxTokens: parseInt(process.env.AI_MAX_TOKENS || '2048'),
      temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
      cacheEnabled: process.env.AI_CACHE_ENABLED !== 'false',
      cacheTTL: parseInt(process.env.AI_CACHE_TTL || '3600')
    };

    if (!this.config.apiKey) {
      throw new Error('GOOGLE_GEMINI_API_KEY is required');
    }
  }

  /**
   * Initialize task-specific configurations
   */
  private initializeTaskConfigs(): void {
    this.taskConfigs.set('resume_analysis', {
      temperature: 0.3,
      maxTokens: 2048,
      topP: 0.95,
      topK: 40
    });

    this.taskConfigs.set('career_recommendations', {
      temperature: 0.7,
      maxTokens: 1024,
      topP: 0.95,
      topK: 40
    });

    this.taskConfigs.set('skills_analysis', {
      temperature: 0.4,
      maxTokens: 1536,
      topP: 0.95,
      topK: 40
    });

    this.taskConfigs.set('interview_prep', {
      temperature: 0.6,
      maxTokens: 2048,
      topP: 0.95,
      topK: 40
    });

    this.taskConfigs.set('personality_analysis', {
      temperature: 0.5,
      maxTokens: 1536,
      topP: 0.9
    });

    this.taskConfigs.set('career_fit_analysis', {
      temperature: 0.6,
      maxTokens: 1024,
      topP: 0.95
    });

    this.taskConfigs.set('skill_gap_analysis', {
      temperature: 0.4,
      maxTokens: 1536,
      topP: 0.95
    });

    this.taskConfigs.set('learning_style_analysis', {
      temperature: 0.5,
      maxTokens: 1024,
      topP: 0.9
    });

    this.taskConfigs.set('market_trend_analysis', {
      temperature: 0.3,
      maxTokens: 1536,
      topP: 0.95
    });

    this.taskConfigs.set('personalized_content', {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 0.95
    });
  }

  /**
   * Generate content with unified caching and error handling
   */
  async generateContent(
    prompt: string,
    taskType: AITaskType,
    cacheKey?: string,
    userId?: string
  ): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // Check cache first
      if (this.config.cacheEnabled && cacheKey) {
        const cached = await this.getCachedResult(cacheKey);
        if (cached) {
          return {
            ...cached,
            cached: true
          };
        }
      }

      // Get task-specific configuration
      const taskConfig = this.taskConfigs.get(taskType) || {
        temperature: this.config.temperature,
        maxTokens: this.config.maxTokens
      };

      // Build generation config
      const generationConfig: GenerationConfig = {
        temperature: taskConfig.temperature,
        maxOutputTokens: taskConfig.maxTokens,
        topP: taskConfig.topP || 0.95,
        topK: taskConfig.topK || 40
      };

      // Generate content
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig
      });

      const response = await result.response;
      const text = response.text();

      // Parse response
      let data;
      try {
        data = JSON.parse(text);
      } catch {
        data = { content: text };
      }

      const processingTime = Date.now() - startTime;
      const aiResponse: AIResponse = {
        success: true,
        data,
        cached: false,
        metadata: {
          tokensUsed: this.estimateTokens(prompt + text),
          processingTime,
          model: this.config.model,
          temperature: generationConfig.temperature,
          generatedAt: new Date().toISOString()
        }
      };

      // Cache the result
      if (this.config.cacheEnabled && cacheKey) {
        await this.cacheResult(cacheKey, aiResponse);
      }

      return aiResponse;

    } catch (error) {
      console.error('AI generation error:', error);
      
      return {
        success: false,
        error: this.handleError(error),
        metadata: {
          processingTime: Date.now() - startTime,
          generatedAt: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Analyze resume with standardized prompt
   */
  async analyzeResume(resumeText: string, userId?: string): Promise<AIResponse> {
    const prompt = this.buildResumeAnalysisPrompt(resumeText);
    const cacheKey = userId ? `resume_analysis_${userId}_${this.hashContent(resumeText)}` : undefined;
    
    return this.generateContent(prompt, 'resume_analysis', cacheKey, userId);
  }

  /**
   * Generate career recommendations
   */
  async generateCareerRecommendations(
    assessmentData: any,
    currentSkills: string[],
    preferences: Record<string, any>,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = this.buildCareerRecommendationsPrompt(assessmentData, currentSkills, preferences);
    const cacheKey = userId ? `career_recommendations_${userId}_${this.hashContent(JSON.stringify({ assessmentData, currentSkills, preferences }))}` : undefined;
    
    return this.generateContent(prompt, 'career_recommendations', cacheKey, userId);
  }

  /**
   * Analyze skills gap
   */
  async analyzeSkillsGap(
    currentSkills: string[],
    targetCareerPath: string,
    experienceLevel: string,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = this.buildSkillsAnalysisPrompt(currentSkills, targetCareerPath, experienceLevel);
    const cacheKey = userId ? `skills_analysis_${userId}_${this.hashContent(JSON.stringify({ currentSkills, targetCareerPath, experienceLevel }))}` : undefined;
    
    return this.generateContent(prompt, 'skills_analysis', cacheKey, userId);
  }

  /**
   * Generate personalized content
   */
  async generatePersonalizedContent(
    taskType: AITaskType,
    context: Record<string, any>,
    metadata: Record<string, any>,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = this.buildPersonalizedPrompt(taskType, context, metadata);
    const cacheKey = userId ? `${taskType}_${userId}_${this.hashContent(JSON.stringify(context))}` : undefined;
    
    return this.generateContent(prompt, taskType, cacheKey, userId);
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: 'Hello, respond with "OK"' }] }],
        generationConfig: {
          temperature: 0.1,
          maxOutputTokens: 10
        }
      });

      const response = await result.response;
      const text = response.text().trim().toLowerCase();
      return text.includes('ok');
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Clear AI cache
   */
  async clearCache(): Promise<void> {
    try {
      // Clear AI-related cache keys
      await cacheService.clear();
      console.log('AI cache cleared successfully');
    } catch (error) {
      console.error('Failed to clear AI cache:', error);
      throw error;
    }
  }

  // Private helper methods
  private async getCachedResult(cacheKey: string): Promise<AIResponse | null> {
    try {
      return await cacheService.getJSON<AIResponse>(`ai:${cacheKey}`);
    } catch (error) {
      console.error('Cache retrieval error:', error);
      return null;
    }
  }

  private async cacheResult(cacheKey: string, result: AIResponse): Promise<void> {
    try {
      await cacheService.setJSON(`ai:${cacheKey}`, result, this.config.cacheTTL);
    } catch (error) {
      console.error('Cache storage error:', error);
    }
  }

  private handleError(error: unknown): string {
    if (error instanceof Error) {
      if (error.message.includes('quota')) {
        return 'AI service quota exceeded. Please try again later.';
      }
      if (error.message.includes('safety')) {
        return 'Content blocked by AI safety filters.';
      }
      if (error.message.includes('rate limit')) {
        return 'AI service rate limit exceeded. Please try again later.';
      }
      return error.message;
    }
    return 'AI service temporarily unavailable';
  }

  private estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }

  private hashContent(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  // Prompt builders will be added in the next part...
}
