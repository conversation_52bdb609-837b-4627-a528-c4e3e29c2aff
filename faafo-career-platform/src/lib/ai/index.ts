// Main AI Service exports
export { aiService, AIService } from './AIService';
export { aiServiceFactory, AIServiceFactory } from './AIServiceFactory';
export { aiConfig, AIConfig } from './config/AIConfig';

// Provider exports
export { AIProvider } from './interfaces/AIProvider';
export { GeminiProvider } from './providers/GeminiProvider';
export { OpenAIProvider } from './providers/OpenAIProvider';
export { AnthropicProvider } from './providers/AnthropicProvider';

// Type exports
export * from './types';

// Convenience re-exports for backward compatibility
export { aiService as geminiService } from './AIService';

/**
 * Quick start guide for the unified AI service:
 * 
 * 1. Basic usage:
 *    import { aiService } from '@/lib/ai';
 *    const result = await aiService.analyzeResume(resumeText, userId);
 * 
 * 2. With specific provider:
 *    const result = await aiService.analyzeResume(resumeText, userId, 'openai');
 * 
 * 3. Health check:
 *    const health = await aiService.healthCheck();
 * 
 * 4. Configuration:
 *    import { aiConfig } from '@/lib/ai';
 *    const providers = aiConfig.getAvailableProviders();
 * 
 * 5. Custom content generation:
 *    const result = await aiService.generateContent({
 *      prompt: 'Your prompt here',
 *      taskType: 'resume_analysis',
 *      userId: 'user123'
 *    });
 */
