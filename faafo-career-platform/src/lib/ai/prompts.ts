/**
 * Centralized prompt builders for AI tasks
 */

export function buildResumeAnalysisPrompt(resumeText: string): string {
  return `
Analyze the following resume and provide a comprehensive assessment in JSON format:

${resumeText}

Please provide your analysis in the following JSON structure:
{
  "strengths": ["list of key strengths"],
  "weaknesses": ["areas for improvement"],
  "suggestions": ["specific improvement suggestions"],
  "skillsIdentified": ["list of skills found in resume"],
  "experienceLevel": "entry|mid|senior|executive",
  "industryFit": ["industries this person would fit well in"],
  "overallScore": 85
}

Focus on actionable insights and specific recommendations.
`;
}

export function buildCareerRecommendationsPrompt(
  assessmentData: any,
  currentSkills: string[],
  preferences: Record<string, any>
): string {
  return `
Based on the following information, provide personalized career recommendations:

Assessment Data: ${JSON.stringify(assessmentData)}
Current Skills: ${currentSkills.join(', ')}
Preferences: ${JSON.stringify(preferences)}

Please provide recommendations in the following JSON structure:
{
  "recommendations": [
    {
      "title": "Career Title",
      "description": "Detailed description",
      "matchScore": 95,
      "requiredSkills": ["skill1", "skill2"],
      "salaryRange": {"min": 50000, "max": 80000, "currency": "USD"},
      "growthPotential": 85,
      "reasoning": "Why this career fits"
    }
  ],
  "topMatch": "Best career match",
  "alternativePaths": ["alternative career paths"]
}

Provide 3-5 recommendations ranked by fit score.
`;
}

export function buildSkillsAnalysisPrompt(
  currentSkills: string[],
  targetCareerPath: string,
  experienceLevel: string
): string {
  return `
Analyze the following skills for career development:

Current Skills: ${currentSkills.join(', ')}
Target Career: ${targetCareerPath}
Experience Level: ${experienceLevel}

Please provide analysis in the following JSON structure:
{
  "currentSkills": [
    {"name": "skill", "level": 8, "category": "technical"}
  ],
  "skillGaps": [
    {"skill": "missing skill", "importance": 9, "learningPath": ["step1", "step2"]}
  ],
  "recommendations": ["specific recommendations"],
  "prioritySkills": ["skills to focus on first"],
  "learningRecommendations": [
    {"skill": "skill name", "priority": "high|medium|low", "timeEstimate": "weeks"}
  ]
}

Focus on actionable learning paths and priority skills.
`;
}

export function buildPersonalizedPrompt(
  taskType: string,
  context: Record<string, any>,
  metadata: Record<string, any>
): string {
  return `
Task: ${taskType}
Context: ${JSON.stringify(context)}
Metadata: ${JSON.stringify(metadata)}

Please provide a comprehensive response based on the task type and context provided.
Return the response in JSON format when possible.
`;
}
