import {
  AIRequest,
  AIResponse,
  AITaskType,
  AIProviderConfig,
  AIProviderCapabilities,
  ResumeAnalysisResponse,
  CareerRecommendationResponse,
  SkillsAnalysisResponse
} from '../types';

/**
 * Abstract base class for AI providers
 * All AI providers must implement this interface
 */
export abstract class AIProvider {
  protected config: AIProviderConfig;
  protected capabilities: AIProviderCapabilities;

  constructor(config: AIProviderConfig) {
    this.config = config;
    this.capabilities = this.getCapabilities();
  }

  /**
   * Get provider capabilities
   */
  abstract getCapabilities(): AIProviderCapabilities;

  /**
   * Get provider name
   */
  abstract getProviderName(): string;

  /**
   * Health check for the provider
   */
  abstract healthCheck(): Promise<boolean>;

  /**
   * Generate content using the AI provider
   */
  abstract generateContent(request: AIRequest): Promise<AIResponse>;

  /**
   * Analyze resume content
   */
  async analyzeResume(
    resumeText: string,
    userId?: string
  ): Promise<AIResponse<ResumeAnalysisResponse>> {
    const request: AIRequest = {
      prompt: this.buildResumeAnalysisPrompt(resumeText),
      taskType: 'resume_analysis',
      userId,
      cacheKey: userId ? `resume_analysis_${userId}_${this.hashContent(resumeText)}` : undefined
    };

    return this.generateContent(request);
  }

  /**
   * Generate career recommendations
   */
  async generateCareerRecommendations(
    assessmentData: any,
    currentSkills: string[],
    preferences: Record<string, any>,
    userId?: string
  ): Promise<AIResponse<CareerRecommendationResponse>> {
    const request: AIRequest = {
      prompt: this.buildCareerRecommendationsPrompt(assessmentData, currentSkills, preferences),
      taskType: 'career_recommendations',
      userId,
      context: { assessmentData, currentSkills, preferences },
      cacheKey: userId ? `career_recommendations_${userId}_${this.hashContent(JSON.stringify({ assessmentData, currentSkills, preferences }))}` : undefined
    };

    return this.generateContent(request);
  }

  /**
   * Analyze skills and provide recommendations
   */
  async analyzeSkills(
    currentSkills: string[],
    targetRole?: string,
    userId?: string
  ): Promise<AIResponse<SkillsAnalysisResponse>> {
    const request: AIRequest = {
      prompt: this.buildSkillsAnalysisPrompt(currentSkills, targetRole),
      taskType: 'skills_analysis',
      userId,
      context: { currentSkills, targetRole },
      cacheKey: userId ? `skills_analysis_${userId}_${this.hashContent(JSON.stringify({ currentSkills, targetRole }))}` : undefined
    };

    return this.generateContent(request);
  }

  /**
   * Generate personalized content
   */
  async generatePersonalizedContent(
    taskType: AITaskType,
    context: Record<string, any>,
    metadata: Record<string, any>,
    userId?: string
  ): Promise<AIResponse> {
    const request: AIRequest = {
      prompt: this.buildPersonalizedPrompt(taskType, context, metadata),
      taskType,
      userId,
      context,
      cacheKey: userId ? `${taskType}_${userId}_${this.hashContent(JSON.stringify(context))}` : undefined
    };

    return this.generateContent(request);
  }

  /**
   * Validate provider configuration
   */
  validateConfig(): boolean {
    return !!(
      this.config.apiKey &&
      this.config.model &&
      this.config.maxTokens > 0 &&
      this.config.temperature >= 0 &&
      this.config.temperature <= 2
    );
  }

  /**
   * Check if provider supports a specific task
   */
  supportsTask(taskType: AITaskType): boolean {
    return this.capabilities.supportedTasks.includes(taskType);
  }

  /**
   * Build resume analysis prompt
   */
  protected buildResumeAnalysisPrompt(resumeText: string): string {
    return `
Analyze the following resume and provide a comprehensive assessment in JSON format:

${resumeText}

Please provide your analysis in the following JSON structure:
{
  "strengths": ["list of key strengths"],
  "weaknesses": ["areas for improvement"],
  "suggestions": ["specific improvement suggestions"],
  "skillsIdentified": ["list of skills found in resume"],
  "experienceLevel": "entry|mid|senior|executive",
  "industryFit": ["industries this person would fit well in"],
  "overallScore": 85
}

Focus on actionable insights and specific recommendations.
`;
  }

  /**
   * Build career recommendations prompt
   */
  protected buildCareerRecommendationsPrompt(
    assessmentData: any,
    currentSkills: string[],
    preferences: Record<string, any>
  ): string {
    return `
Based on the following information, provide personalized career recommendations:

Assessment Data: ${JSON.stringify(assessmentData)}
Current Skills: ${currentSkills.join(', ')}
Preferences: ${JSON.stringify(preferences)}

Please provide recommendations in the following JSON structure:
{
  "recommendations": [
    {
      "title": "Career Title",
      "description": "Detailed description",
      "matchScore": 95,
      "requiredSkills": ["skill1", "skill2"],
      "salaryRange": {"min": 50000, "max": 80000, "currency": "USD"},
      "growthPotential": 85,
      "reasoning": "Why this career fits"
    }
  ],
  "topMatch": "Best career match",
  "alternativePaths": ["alternative career paths"]
}

Provide 3-5 recommendations ranked by fit score.
`;
  }

  /**
   * Build skills analysis prompt
   */
  protected buildSkillsAnalysisPrompt(currentSkills: string[], targetRole?: string): string {
    const targetRoleText = targetRole ? `Target Role: ${targetRole}` : '';
    
    return `
Analyze the following skills and provide recommendations:

Current Skills: ${currentSkills.join(', ')}
${targetRoleText}

Please provide analysis in the following JSON structure:
{
  "currentSkills": [
    {"name": "skill", "level": 8, "category": "technical"}
  ],
  "skillGaps": [
    {"skill": "missing skill", "importance": 9, "learningPath": ["step1", "step2"]}
  ],
  "recommendations": ["specific recommendations"],
  "prioritySkills": ["skills to focus on first"]
}

Focus on actionable learning paths and priority skills.
`;
  }

  /**
   * Build personalized prompt based on task type
   */
  protected buildPersonalizedPrompt(
    taskType: AITaskType,
    context: Record<string, any>,
    metadata: Record<string, any>
  ): string {
    // This is a base implementation - providers can override for specific needs
    return `
Task: ${taskType}
Context: ${JSON.stringify(context)}
Metadata: ${JSON.stringify(metadata)}

Please provide a comprehensive response based on the task type and context provided.
`;
  }

  /**
   * Hash content for cache keys
   */
  protected hashContent(content: string): string {
    // Simple hash function - in production, consider using crypto
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}
