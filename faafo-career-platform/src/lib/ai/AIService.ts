import {
  AIRequest,
  AIResponse,
  AITaskType,
  AIProvider as AIProviderType,
  ResumeAnalysisResponse,
  CareerRecommendationResponse,
  SkillsAnalysisResponse,
  AIProviderError,
  AIRateLimitError
} from './types';
import { aiServiceFactory } from './AIServiceFactory';
import { aiConfig } from './config/AIConfig';
import { cacheService } from '../services/cacheService';

/**
 * Unified AI Service - Main interface for all AI operations
 */
export class AIService {
  private static instance: AIService;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  /**
   * Analyze resume content
   */
  async analyzeResume(
    resumeText: string,
    userId?: string,
    preferredProvider?: AIProviderType
  ): Promise<AIResponse<ResumeAnalysisResponse>> {
    return this.executeWithCaching(
      'resume_analysis',
      async (provider) => provider.analyzeResume(resumeText, userId),
      {
        cacheKey: userId ? `resume_analysis_${userId}_${this.hashContent(resumeText)}` : undefined,
        preferredProvider,
        userId
      }
    );
  }

  /**
   * Generate career recommendations
   */
  async generateCareerRecommendations(
    assessmentData: any,
    currentSkills: string[],
    preferences: Record<string, any>,
    userId?: string,
    preferredProvider?: AIProviderType
  ): Promise<AIResponse<CareerRecommendationResponse>> {
    return this.executeWithCaching(
      'career_recommendations',
      async (provider) => provider.generateCareerRecommendations(assessmentData, currentSkills, preferences, userId),
      {
        cacheKey: userId ? `career_recommendations_${userId}_${this.hashContent(JSON.stringify({ assessmentData, currentSkills, preferences }))}` : undefined,
        preferredProvider,
        userId
      }
    );
  }

  /**
   * Analyze skills and provide recommendations
   */
  async analyzeSkills(
    currentSkills: string[],
    targetRole?: string,
    userId?: string,
    preferredProvider?: AIProviderType
  ): Promise<AIResponse<SkillsAnalysisResponse>> {
    return this.executeWithCaching(
      'skills_analysis',
      async (provider) => provider.analyzeSkills(currentSkills, targetRole, userId),
      {
        cacheKey: userId ? `skills_analysis_${userId}_${this.hashContent(JSON.stringify({ currentSkills, targetRole }))}` : undefined,
        preferredProvider,
        userId
      }
    );
  }

  /**
   * Generate personalized content
   */
  async generatePersonalizedContent(
    taskType: AITaskType,
    context: Record<string, any>,
    metadata: Record<string, any>,
    userId?: string,
    preferredProvider?: AIProviderType
  ): Promise<AIResponse> {
    return this.executeWithCaching(
      taskType,
      async (provider) => provider.generatePersonalizedContent(taskType, context, metadata, userId),
      {
        cacheKey: userId ? `${taskType}_${userId}_${this.hashContent(JSON.stringify(context))}` : undefined,
        preferredProvider,
        userId
      }
    );
  }

  /**
   * Generate content with custom prompt
   */
  async generateContent(
    request: AIRequest,
    preferredProvider?: AIProviderType
  ): Promise<AIResponse> {
    return this.executeWithCaching(
      request.taskType,
      async (provider) => provider.generateContent(request),
      {
        cacheKey: request.cacheKey,
        preferredProvider,
        userId: request.userId
      }
    );
  }

  /**
   * Execute AI operation with caching, error handling, and provider fallback
   */
  private async executeWithCaching<T>(
    taskType: AITaskType,
    operation: (provider: any) => Promise<AIResponse<T>>,
    options: {
      cacheKey?: string;
      preferredProvider?: AIProviderType;
      userId?: string;
      retryCount?: number;
    } = {}
  ): Promise<AIResponse<T>> {
    const { cacheKey, preferredProvider, userId, retryCount = 0 } = options;
    const cacheConfig = aiConfig.getCacheConfig();

    try {
      // Check cache first
      if (cacheConfig.enabled && cacheKey) {
        const cached = await this.getCachedResult<T>(cacheKey);
        if (cached) {
          return {
            ...cached,
            cached: true
          };
        }
      }

      // Get appropriate provider
      const provider = preferredProvider
        ? aiServiceFactory.getProvider(preferredProvider)
        : aiServiceFactory.getBestProviderForTask(taskType, preferredProvider);

      // Execute operation
      const result = await operation(provider);

      // Cache successful results
      if (result.success && cacheConfig.enabled && cacheKey) {
        await this.cacheResult(cacheKey, result, cacheConfig.ttl);
      }

      return result;

    } catch (error) {
      console.error(`AI operation failed for task ${taskType}:`, error);

      // Handle rate limiting with retry
      if (error instanceof AIRateLimitError && retryCount < 2) {
        console.log(`Rate limited, retrying with different provider...`);
        const fallbackProvider = this.getFallbackProvider(preferredProvider);
        return this.executeWithCaching(taskType, operation, {
          ...options,
          preferredProvider: fallbackProvider,
          retryCount: retryCount + 1
        });
      }

      // Handle provider errors with fallback
      if (error instanceof AIProviderError && retryCount < 1) {
        console.log(`Provider error, trying fallback provider...`);
        const fallbackProvider = this.getFallbackProvider(preferredProvider);
        return this.executeWithCaching(taskType, operation, {
          ...options,
          preferredProvider: fallbackProvider,
          retryCount: retryCount + 1
        });
      }

      // Return error response
      return {
        success: false,
        error: error instanceof Error ? error.message : 'AI service unavailable',
        metadata: {
          generatedAt: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Get cached result
   */
  private async getCachedResult<T>(cacheKey: string): Promise<AIResponse<T> | null> {
    try {
      const cacheConfig = aiConfig.getCacheConfig();
      const fullKey = `${cacheConfig.keyPrefix}${cacheKey}`;
      return await cacheService.getJSON<AIResponse<T>>(fullKey);
    } catch (error) {
      console.error('Cache retrieval error:', error);
      return null;
    }
  }

  /**
   * Cache result
   */
  private async cacheResult<T>(cacheKey: string, result: AIResponse<T>, ttl: number): Promise<void> {
    try {
      const cacheConfig = aiConfig.getCacheConfig();
      const fullKey = `${cacheConfig.keyPrefix}${cacheKey}`;
      await cacheService.setJSON(fullKey, result, ttl);
    } catch (error) {
      console.error('Cache storage error:', error);
    }
  }

  /**
   * Get fallback provider
   */
  private getFallbackProvider(currentProvider?: AIProviderType): AIProviderType | undefined {
    const available = aiServiceFactory.getAvailableProviders();
    const filtered = currentProvider 
      ? available.filter(p => p !== currentProvider)
      : available;

    return filtered.length > 0 ? filtered[0] : undefined;
  }

  /**
   * Hash content for cache keys
   */
  private hashContent(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Health check for all providers
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    providers: Record<AIProviderType, boolean>;
    defaultProvider: AIProviderType;
  }> {
    const results = await aiServiceFactory.testAllProviders();
    const healthyCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (healthyCount === totalCount) {
      status = 'healthy';
    } else if (healthyCount > 0) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      providers: results,
      defaultProvider: aiConfig.getDefaultProvider()
    };
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      factory: aiServiceFactory.getProviderStats(),
      config: {
        defaultProvider: aiConfig.getDefaultProvider(),
        availableProviders: aiConfig.getAvailableProviders(),
        cacheEnabled: aiConfig.getCacheConfig().enabled
      }
    };
  }

  /**
   * Clear AI-related cache
   */
  async clearCache(): Promise<void> {
    try {
      const cacheConfig = aiConfig.getCacheConfig();
      // This is a simplified approach - in production, you might want to clear only AI-related keys
      await cacheService.clear();
      console.log('AI cache cleared successfully');
    } catch (error) {
      console.error('Failed to clear AI cache:', error);
      throw error;
    }
  }

  /**
   * Reload configuration and providers
   */
  reload(): void {
    aiServiceFactory.reload();
  }
}

// Export singleton instance
export const aiService = AIService.getInstance();
