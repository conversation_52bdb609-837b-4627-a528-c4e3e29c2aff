/**
 * Redis Cache Integration for Distributed Caching
 * Provides advanced caching strategies with Redis backend
 */

import { Redis } from 'ioredis';
import { Prisma } from '@prisma/client';

export interface CacheConfig {
  enabled: boolean;
  redisUrl?: string;
  defaultTTL: number;
  maxRetries: number;
  retryDelayOnFailover: number;
  enableOfflineQueue: boolean;
  keyPrefix: string;
}

export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  hitRate: number;
  totalOperations: number;
}

export interface CacheWarmingStrategy {
  pattern: string;
  query: () => Promise<any>;
  ttl?: number;
  schedule?: string; // cron expression
}

class RedisCache {
  private redis: Redis | null = null;
  private fallbackCache = new Map<string, { data: any; expiry: number }>();
  private config: CacheConfig;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
    hitRate: 0,
    totalOperations: 0,
  };
  private warmingStrategies: CacheWarmingStrategy[] = [];

  constructor() {
    this.config = {
      enabled: process.env.REDIS_CACHE_ENABLED === 'true',
      redisUrl: process.env.REDIS_URL,
      defaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL || '300'), // 5 minutes
      maxRetries: 3,
      retryDelayOnFailover: 100,
      enableOfflineQueue: false,
      keyPrefix: 'faafo:cache:',
    };

    this.initializeRedis();
  }

  /**
   * Initialize Redis connection
   */
  private async initializeRedis(): Promise<void> {
    if (!this.config.enabled || !this.config.redisUrl) {
      console.log('Redis cache disabled or URL not provided, using fallback cache');
      return;
    }

    try {
      this.redis = new Redis(this.config.redisUrl, {
        maxRetriesPerRequest: this.config.maxRetries,
        retryDelayOnFailover: this.config.retryDelayOnFailover,
        enableOfflineQueue: this.config.enableOfflineQueue,
        lazyConnect: true,
      });

      this.redis.on('connect', () => {
        console.log('Redis cache connected successfully');
      });

      this.redis.on('error', (error) => {
        console.error('Redis cache error:', error);
        this.stats.errors++;
      });

      this.redis.on('close', () => {
        console.log('Redis cache connection closed');
      });

      // Test connection
      await this.redis.ping();
    } catch (error) {
      console.error('Failed to initialize Redis cache:', error);
      this.redis = null;
    }
  }

  /**
   * Generate cache key
   */
  private generateKey(params: Prisma.MiddlewareParams): string {
    const baseKey = `${params.model}.${params.action}`;
    const argsHash = this.hashArgs(params.args);
    return `${this.config.keyPrefix}${baseKey}:${argsHash}`;
  }

  /**
   * Hash arguments for consistent cache keys
   */
  private hashArgs(args: any): string {
    if (!args) return 'no-args';
    
    try {
      const normalized = this.normalizeArgs(args);
      const str = JSON.stringify(normalized);
      return Buffer.from(str).toString('base64').substring(0, 32);
    } catch (error) {
      return 'hash-error';
    }
  }

  /**
   * Normalize arguments for consistent hashing
   */
  private normalizeArgs(args: any): any {
    if (!args || typeof args !== 'object') return args;

    const normalized: any = {};
    
    // Sort keys for consistent ordering
    const sortedKeys = Object.keys(args).sort();
    
    for (const key of sortedKeys) {
      const value = args[key];
      
      if (Array.isArray(value)) {
        normalized[key] = value.sort();
      } else if (value && typeof value === 'object') {
        normalized[key] = this.normalizeArgs(value);
      } else {
        normalized[key] = value;
      }
    }

    return normalized;
  }

  /**
   * Get cached result
   */
  async get(params: Prisma.MiddlewareParams): Promise<any | null> {
    const key = this.generateKey(params);
    this.stats.totalOperations++;

    try {
      let result = null;

      if (this.redis) {
        const cached = await this.redis.get(key);
        if (cached) {
          result = JSON.parse(cached);
        }
      } else {
        // Fallback to in-memory cache
        const cached = this.fallbackCache.get(key);
        if (cached && Date.now() < cached.expiry) {
          result = cached.data;
        } else if (cached) {
          this.fallbackCache.delete(key);
        }
      }

      if (result !== null) {
        this.stats.hits++;
        this.updateHitRate();
        return result;
      } else {
        this.stats.misses++;
        this.updateHitRate();
        return null;
      }
    } catch (error) {
      console.error('Cache get error:', error);
      this.stats.errors++;
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * Set cached result
   */
  async set(params: Prisma.MiddlewareParams, data: any, ttl?: number): Promise<void> {
    const key = this.generateKey(params);
    const cacheTTL = ttl || this.config.defaultTTL;

    try {
      if (this.redis) {
        await this.redis.setex(key, cacheTTL, JSON.stringify(data));
      } else {
        // Fallback to in-memory cache
        const expiry = Date.now() + (cacheTTL * 1000);
        this.fallbackCache.set(key, { data, expiry });
        
        // Implement simple LRU eviction
        if (this.fallbackCache.size > 1000) {
          const firstKey = this.fallbackCache.keys().next().value;
          if (firstKey) {
            this.fallbackCache.delete(firstKey);
          }
        }
      }

      this.stats.sets++;
    } catch (error) {
      console.error('Cache set error:', error);
      this.stats.errors++;
    }
  }

  /**
   * Delete cached result
   */
  async delete(pattern: string): Promise<void> {
    try {
      if (this.redis) {
        const keys = await this.redis.keys(`${this.config.keyPrefix}${pattern}*`);
        if (keys.length > 0) {
          await this.redis.del(...keys);
          this.stats.deletes += keys.length;
        }
      } else {
        // Fallback cache deletion
        const keysToDelete: string[] = [];
        for (const key of this.fallbackCache.keys()) {
          if (key.includes(pattern)) {
            keysToDelete.push(key);
          }
        }
        keysToDelete.forEach(key => this.fallbackCache.delete(key));
        this.stats.deletes += keysToDelete.length;
      }
    } catch (error) {
      console.error('Cache delete error:', error);
      this.stats.errors++;
    }
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    try {
      if (this.redis) {
        const keys = await this.redis.keys(`${this.config.keyPrefix}*`);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      } else {
        this.fallbackCache.clear();
      }
      
      // Reset stats
      this.stats = {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        errors: 0,
        hitRate: 0,
        totalOperations: 0,
      };
    } catch (error) {
      console.error('Cache clear error:', error);
      this.stats.errors++;
    }
  }

  /**
   * Update hit rate calculation
   */
  private updateHitRate(): void {
    const totalRequests = this.stats.hits + this.stats.misses;
    this.stats.hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Check if query should be cached
   */
  shouldCache(params: Prisma.MiddlewareParams): boolean {
    if (!this.config.enabled) return false;

    // Only cache read operations
    const readOperations = ['findFirst', 'findMany', 'findUnique', 'count', 'aggregate'];
    if (!readOperations.includes(params.action)) return false;

    // Don't cache queries with certain conditions
    if (params.args?.where?.createdAt || params.args?.where?.updatedAt) {
      return false; // Don't cache time-sensitive queries
    }

    return true;
  }

  /**
   * Add cache warming strategy
   */
  addWarmingStrategy(strategy: CacheWarmingStrategy): void {
    this.warmingStrategies.push(strategy);
  }

  /**
   * Warm cache with predefined strategies
   */
  async warmCache(): Promise<void> {
    console.log('Starting cache warming...');
    
    for (const strategy of this.warmingStrategies) {
      try {
        const data = await strategy.query();
        const key = `${this.config.keyPrefix}warm:${strategy.pattern}`;
        const ttl = strategy.ttl || this.config.defaultTTL;
        
        if (this.redis) {
          await this.redis.setex(key, ttl, JSON.stringify(data));
        } else {
          const expiry = Date.now() + (ttl * 1000);
          this.fallbackCache.set(key, { data, expiry });
        }
        
        console.log(`Cache warmed for pattern: ${strategy.pattern}`);
      } catch (error) {
        console.error(`Failed to warm cache for pattern ${strategy.pattern}:`, error);
      }
    }
    
    console.log('Cache warming completed');
  }

  /**
   * Invalidate cache based on model changes
   */
  async invalidateByModel(model: string): Promise<void> {
    await this.delete(`${model}.`);
    console.log(`Cache invalidated for model: ${model}`);
  }

  /**
   * Get cache health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    redis: boolean;
    fallback: boolean;
    errorRate: number;
    hitRate: number;
  }> {
    const errorRate = this.stats.totalOperations > 0 
      ? (this.stats.errors / this.stats.totalOperations) * 100 
      : 0;

    let redisHealthy = false;
    try {
      if (this.redis) {
        await this.redis.ping();
        redisHealthy = true;
      }
    } catch (error) {
      redisHealthy = false;
    }

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (redisHealthy && errorRate < 5) {
      status = 'healthy';
    } else if (redisHealthy || errorRate < 20) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      redis: redisHealthy,
      fallback: !redisHealthy,
      errorRate,
      hitRate: this.stats.hitRate,
    };
  }

  /**
   * Prisma middleware for Redis caching
   */
  middleware(): Prisma.Middleware {
    return async (params: Prisma.MiddlewareParams, next) => {
      if (!this.shouldCache(params)) {
        return next(params);
      }

      // Try to get from cache
      const cached = await this.get(params);
      if (cached !== null) {
        return cached;
      }

      // Execute query and cache result
      const result = await next(params);
      
      if (result !== null && result !== undefined) {
        await this.set(params, result);
      }

      return result;
    };
  }
}

// Singleton instance
export const redisCache = new RedisCache();

// Export middleware
export const redisCacheMiddleware = redisCache.middleware();

export default redisCache;
