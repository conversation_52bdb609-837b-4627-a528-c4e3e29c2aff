/**
 * Production-Optimized Database Connection Pool Manager
 * Enhanced connection pooling with auto-scaling, tier-based configuration, and advanced monitoring
 */

import { PrismaClient } from '@prisma/client';
import { EventEmitter } from 'events';

export interface ProductionConnectionPoolConfig {
  minConnections: number;
  maxConnections: number;
  connectionTimeout: number;
  idleTimeout: number;
  maxLifetime: number;
  healthCheckInterval: number;
  retryAttempts: number;
  retryDelay: number;
  // Production-specific settings
  environment: 'development' | 'staging' | 'production';
  databaseTier: 'hobby' | 'basic' | 'standard' | 'premium' | 'enterprise';
  enableAutoScaling: boolean;
  scaleUpThreshold: number;
  scaleDownThreshold: number;
  maxScaleUpConnections: number;
  connectionLeakDetection: boolean;
  connectionLeakTimeout: number;
  slowQueryThreshold: number;
  enableMetrics: boolean;
  metricsRetentionHours: number;
  enableConnectionWarmup: boolean;
  warmupConnections: number;
  enableLoadBalancing: boolean;
  readReplicaRatio: number;
}

export interface ProductionConnectionPoolStats {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingRequests: number;
  totalRequests: number;
  successfulConnections: number;
  failedConnections: number;
  averageConnectionTime: number;
  peakConnections: number;
  lastHealthCheck: Date;
  uptime: number;
  // Production-specific stats
  autoScalingEvents: number;
  connectionLeaksDetected: number;
  slowQueries: number;
  readReplicaConnections: number;
  writeConnections: number;
  connectionWarmupTime: number;
}

export interface DatabaseTierConfig {
  tier: string;
  maxConnections: number;
  minConnections: number;
  connectionTimeout: number;
  idleTimeout: number;
  healthCheckInterval: number;
  enableAutoScaling: boolean;
  scaleUpThreshold: number;
  scaleDownThreshold: number;
}

export class ProductionDatabaseConnectionPool extends EventEmitter {
  private config: ProductionConnectionPoolConfig;
  private stats: ProductionConnectionPoolStats;
  private metrics: any[] = [];
  private healthCheckTimer?: NodeJS.Timeout;
  private autoScalingTimer?: NodeJS.Timeout;
  private startTime: Date;
  private isShuttingDown = false;
  private connectionLeaks = new Map<string, Date>();
  private lastScalingEvent = new Date();

  constructor(config: Partial<ProductionConnectionPoolConfig> = {}) {
    super();
    
    const environment = (process.env.NODE_ENV || 'development') as 'development' | 'staging' | 'production';
    const databaseTier = (process.env.DATABASE_TIER || 'basic') as 'hobby' | 'basic' | 'standard' | 'premium' | 'enterprise';
    
    // Get tier-specific configuration
    const tierConfig = this.getTierConfiguration(databaseTier);
    
    this.config = {
      // Default values
      enableAutoScaling: false,
      enableConnectionWarmup: false,
      enableMetrics: true,
      enableLogging: true,
      connectionLeakDetection: true,
      connectionLeakTimeout: 300000,
      slowQueryThreshold: 1000,
      metricsRetentionHours: 24,
      warmupConnections: 3,
      readReplicaRatio: 0.7,
      // Merge tier config
      ...tierConfig,
      enableAutoScaling: process.env.DB_ENABLE_AUTO_SCALING === 'true',
      connectionLeakDetection: process.env.DB_CONNECTION_LEAK_DETECTION === 'true',
      connectionLeakTimeout: parseInt(process.env.DB_CONNECTION_LEAK_TIMEOUT || '300000'), // 5 minutes
      slowQueryThreshold: parseInt(process.env.DB_SLOW_QUERY_THRESHOLD || '1000'),
      enableMetrics: process.env.DB_ENABLE_METRICS !== 'false',
      metricsRetentionHours: parseInt(process.env.DB_METRICS_RETENTION_HOURS || '24'),
      enableConnectionWarmup: process.env.DB_ENABLE_CONNECTION_WARMUP === 'true',
      warmupConnections: parseInt(process.env.DB_WARMUP_CONNECTIONS || '2'),
      enableLoadBalancing: process.env.DB_ENABLE_LOAD_BALANCING === 'true',
      readReplicaRatio: parseFloat(process.env.DB_READ_REPLICA_RATIO || '0.7'),
      ...config,
    };

    this.startTime = new Date();
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingRequests: 0,
      totalRequests: 0,
      successfulConnections: 0,
      failedConnections: 0,
      averageConnectionTime: 0,
      peakConnections: 0,
      lastHealthCheck: new Date(),
      uptime: 0,
      autoScalingEvents: 0,
      connectionLeaksDetected: 0,
      slowQueries: 0,
      readReplicaConnections: 0,
      writeConnections: 0,
      connectionWarmupTime: 0,
    };

    this.initialize();
  }

  /**
   * Get database tier-specific configuration
   */
  private getTierConfiguration(tier: string): Partial<ProductionConnectionPoolConfig> {
    const tierConfigs: Record<string, DatabaseTierConfig> = {
      hobby: {
        tier: 'hobby',
        maxConnections: 5,
        minConnections: 1,
        connectionTimeout: 5000,
        idleTimeout: 30000,
        healthCheckInterval: 60000,
        enableAutoScaling: false,
        scaleUpThreshold: 0.8,
        scaleDownThreshold: 0.3,
      },
      basic: {
        tier: 'basic',
        maxConnections: 20,
        minConnections: 2,
        connectionTimeout: 10000,
        idleTimeout: 30000,
        healthCheckInterval: 30000,
        enableAutoScaling: true,
        scaleUpThreshold: 0.8,
        scaleDownThreshold: 0.3,
      },
      standard: {
        tier: 'standard',
        maxConnections: 50,
        minConnections: 5,
        connectionTimeout: 15000,
        idleTimeout: 60000,
        healthCheckInterval: 30000,
        enableAutoScaling: true,
        scaleUpThreshold: 0.75,
        scaleDownThreshold: 0.25,
      },
      premium: {
        tier: 'premium',
        maxConnections: 100,
        minConnections: 10,
        connectionTimeout: 20000,
        idleTimeout: 120000,
        healthCheckInterval: 15000,
        enableAutoScaling: true,
        scaleUpThreshold: 0.7,
        scaleDownThreshold: 0.2,
      },
      enterprise: {
        tier: 'enterprise',
        maxConnections: 200,
        minConnections: 20,
        connectionTimeout: 30000,
        idleTimeout: 300000,
        healthCheckInterval: 10000,
        enableAutoScaling: true,
        scaleUpThreshold: 0.65,
        scaleDownThreshold: 0.15,
      },
    };

    const config = tierConfigs[tier] || tierConfigs.basic;
    
    return {
      minConnections: config.minConnections,
      maxConnections: config.maxConnections,
      connectionTimeout: config.connectionTimeout,
      idleTimeout: config.idleTimeout,
      maxLifetime: 3600000, // 1 hour
      healthCheckInterval: config.healthCheckInterval,
      retryAttempts: 3,
      retryDelay: 1000,
      scaleUpThreshold: config.scaleUpThreshold,
      scaleDownThreshold: config.scaleDownThreshold,
      maxScaleUpConnections: Math.floor(config.maxConnections * 1.5),
    };
  }

  /**
   * Initialize the connection pool
   */
  private async initialize(): Promise<void> {
    console.log(`Initializing ${this.config.databaseTier} tier connection pool for ${this.config.environment}`);
    
    this.startHealthChecks();
    
    if (this.config.enableAutoScaling) {
      this.startAutoScaling();
    }
    
    if (this.config.enableConnectionWarmup) {
      await this.warmupConnections();
    }
    
    this.emit('initialized', {
      config: this.config,
    });
  }

  /**
   * Warm up connections on startup
   */
  private async warmupConnections(): Promise<void> {
    const startTime = Date.now();
    console.log(`Warming up ${this.config.warmupConnections} connections...`);
    
    try {
      const { prisma } = await import('../prisma');
      
      // Create warmup connections
      const warmupPromises = Array.from({ length: this.config.warmupConnections }, async () => {
        return prisma.$queryRaw`SELECT 1 as warmup`;
      });
      
      await Promise.all(warmupPromises);
      
      this.stats.connectionWarmupTime = Date.now() - startTime;
      console.log(`Connection warmup completed in ${this.stats.connectionWarmupTime}ms`);
      
    } catch (error) {
      console.error('Connection warmup failed:', error);
    }
  }

  /**
   * Start auto-scaling monitoring
   */
  private startAutoScaling(): void {
    if (this.autoScalingTimer) {
      clearInterval(this.autoScalingTimer);
    }

    this.autoScalingTimer = setInterval(() => {
      if (!this.isShuttingDown) {
        this.evaluateAutoScaling();
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Evaluate auto-scaling needs
   */
  private evaluateAutoScaling(): void {
    const utilizationRate = this.stats.activeConnections / this.config.maxConnections;
    const timeSinceLastScaling = Date.now() - this.lastScalingEvent.getTime();
    
    // Prevent rapid scaling events (minimum 2 minutes between scaling)
    if (timeSinceLastScaling < 120000) {
      return;
    }

    if (utilizationRate > this.config.scaleUpThreshold) {
      this.scaleUp();
    } else if (utilizationRate < this.config.scaleDownThreshold) {
      this.scaleDown();
    }
  }

  /**
   * Scale up connection pool
   */
  private scaleUp(): void {
    const newMaxConnections = Math.min(
      this.config.maxConnections + 5,
      this.config.maxScaleUpConnections
    );
    
    if (newMaxConnections > this.config.maxConnections) {
      console.log(`Scaling up connection pool from ${this.config.maxConnections} to ${newMaxConnections}`);
      this.config.maxConnections = newMaxConnections;
      this.stats.autoScalingEvents++;
      this.lastScalingEvent = new Date();
      
      this.emit('scaleUp', {
        oldMax: this.config.maxConnections - 5,
        newMax: newMaxConnections,
        utilizationRate: this.stats.activeConnections / this.config.maxConnections,
      });
    }
  }

  /**
   * Scale down connection pool
   */
  private scaleDown(): void {
    const tierConfig = this.getTierConfiguration(this.config.databaseTier);
    const newMaxConnections = Math.max(
      this.config.maxConnections - 2,
      tierConfig.maxConnections || 20
    );
    
    if (newMaxConnections < this.config.maxConnections) {
      console.log(`Scaling down connection pool from ${this.config.maxConnections} to ${newMaxConnections}`);
      this.config.maxConnections = newMaxConnections;
      this.stats.autoScalingEvents++;
      this.lastScalingEvent = new Date();
      
      this.emit('scaleDown', {
        oldMax: this.config.maxConnections + 2,
        newMax: newMaxConnections,
        utilizationRate: this.stats.activeConnections / this.config.maxConnections,
      });
    }
  }

  /**
   * Start periodic health checks
   */
  private startHealthChecks(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(async () => {
      if (!this.isShuttingDown) {
        await this.performHealthCheck();
      }
    }, this.config.healthCheckInterval);
  }

  /**
   * Perform comprehensive health check
   */
  private async performHealthCheck(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const { prisma } = await import('../prisma');
      
      // Basic connectivity check
      await prisma.$queryRaw`SELECT 1 as health_check`;
      
      // Check connection pool status
      const poolStatus = await this.getPoolStatus();
      
      this.stats.lastHealthCheck = new Date();
      
      const healthData = {
        success: true,
        latency: Date.now() - startTime,
        poolStatus,
      };
      
      this.emit('healthCheck', healthData);
      
      // Check for connection leaks if enabled
      if (this.config.connectionLeakDetection) {
        this.detectConnectionLeaks();
      }
      
    } catch (error) {
      this.emit('healthCheck', { 
        success: false, 
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      console.error('Database health check failed:', error);
    }
  }

  /**
   * Get detailed pool status
   */
  private async getPoolStatus(): Promise<any> {
    return {
      activeConnections: this.stats.activeConnections,
      maxConnections: this.config.maxConnections,
      utilizationRate: (this.stats.activeConnections / this.config.maxConnections) * 100,
      autoScalingEnabled: this.config.enableAutoScaling,
      autoScalingEvents: this.stats.autoScalingEvents,
      connectionLeaksDetected: this.stats.connectionLeaksDetected,
      slowQueries: this.stats.slowQueries,
    };
  }

  /**
   * Detect connection leaks
   */
  private detectConnectionLeaks(): void {
    const now = new Date();
    let leaksDetected = 0;
    
    this.connectionLeaks.forEach((timestamp, connectionId) => {
      const connectionAge = now.getTime() - timestamp.getTime();
      
      if (connectionAge > this.config.connectionLeakTimeout) {
        console.warn(`Connection leak detected: ${connectionId} (age: ${connectionAge}ms)`);
        this.connectionLeaks.delete(connectionId);
        leaksDetected++;
      }
    });
    
    if (leaksDetected > 0) {
      this.stats.connectionLeaksDetected += leaksDetected;
      this.emit('connectionLeak', {
        count: leaksDetected,
        totalDetected: this.stats.connectionLeaksDetected,
      });
    }
  }

  /**
   * Record connection metrics with production enhancements
   */
  recordMetrics(metrics: any): void {
    const metric = {
      ...metrics,
      timestamp: new Date(),
    };

    this.metrics.push(metric);
    
    // Keep metrics based on retention policy
    const retentionMs = this.config.metricsRetentionHours * 60 * 60 * 1000;
    const cutoffTime = Date.now() - retentionMs;
    this.metrics = this.metrics.filter(m => m.timestamp.getTime() > cutoffTime);

    // Update statistics
    this.updateStats(metric);
    
    // Track slow queries
    if (metrics.queryTime > this.config.slowQueryThreshold) {
      this.stats.slowQueries++;
      this.emit('slowQuery', {
        queryTime: metrics.queryTime,
        threshold: this.config.slowQueryThreshold,
        query: metrics.query,
      });
    }
  }

  /**
   * Update connection statistics
   */
  private updateStats(metric: any): void {
    this.stats.totalRequests++;
    
    if (metric.success) {
      this.stats.successfulConnections++;
    } else {
      this.stats.failedConnections++;
    }

    // Calculate average connection time
    const recentMetrics = this.metrics.slice(-100);
    const totalTime = recentMetrics.reduce((sum, m) => sum + m.connectionTime, 0);
    this.stats.averageConnectionTime = totalTime / recentMetrics.length;

    // Update peak connections
    if (this.stats.activeConnections > this.stats.peakConnections) {
      this.stats.peakConnections = this.stats.activeConnections;
    }

    // Update uptime
    this.stats.uptime = Date.now() - this.startTime.getTime();
  }

  /**
   * Get production-enhanced statistics
   */
  getStats(): ProductionConnectionPoolStats {
    return { ...this.stats };
  }

  /**
   * Get recent metrics
   */
  getMetrics(limit: number = 100): any[] {
    return this.metrics.slice(-limit);
  }

  /**
   * Reset metrics and statistics
   */
  reset(): void {
    this.metrics = [];
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingRequests: 0,
      totalRequests: 0,
      successfulConnections: 0,
      failedConnections: 0,
      averageConnectionTime: 0,
      peakConnections: 0,
      lastHealthCheck: new Date(),
      uptime: 0,
      autoScalingEvents: 0,
      connectionLeaksDetected: 0,
      slowQueries: 0,
      readReplicaConnections: 0,
      writeConnections: 0,
      connectionWarmupTime: 0,
    };
    this.startTime = new Date();
    console.log('Connection pool metrics reset');
  }

  /**
   * Optimize configuration based on current usage
   */
  optimizeConfiguration(): string[] {
    const stats = this.getStats();
    const suggestions: string[] = [];
    const utilizationRate = stats.activeConnections / this.config.maxConnections;

    // Analyze usage patterns and suggest optimizations
    if (utilizationRate > 0.9) {
      suggestions.push('High utilization detected. Consider increasing maxConnections.');
    } else if (utilizationRate < 0.2) {
      suggestions.push('Low utilization detected. Consider reducing maxConnections to save resources.');
    }

    if (stats.averageConnectionTime > 1000) {
      suggestions.push('High connection time. Consider optimizing database queries or network configuration.');
    }

    if (stats.autoScalingEvents > 10) {
      suggestions.push('Frequent auto-scaling events. Consider adjusting base connection pool size.');
    }

    if (stats.connectionLeaksDetected > 0) {
      suggestions.push('Connection leaks detected. Review application code for proper connection handling.');
    }

    if (suggestions.length === 0) {
      suggestions.push('Connection pool configuration appears optimal for current usage patterns.');
    }

    return suggestions;
  }

  /**
   * Get production-specific recommendations
   */
  getProductionRecommendations(): string[] {
    const recommendations: string[] = [];
    const stats = this.getStats();
    const utilizationRate = stats.activeConnections / this.config.maxConnections;

    // Tier-specific recommendations
    if (this.config.databaseTier === 'hobby' && stats.peakConnections > 3) {
      recommendations.push('Consider upgrading to Basic tier for better performance and more connections.');
    }

    if (utilizationRate > 0.9) {
      recommendations.push('Connection pool utilization is very high. Consider upgrading database tier or optimizing queries.');
    }

    if (stats.autoScalingEvents > 10) {
      recommendations.push('Frequent auto-scaling events detected. Consider increasing base connection pool size.');
    }

    if (stats.connectionLeaksDetected > 0) {
      recommendations.push('Connection leaks detected. Review application code for proper connection handling.');
    }

    if (stats.slowQueries > stats.totalRequests * 0.1) {
      recommendations.push('High number of slow queries detected. Consider query optimization or adding indexes.');
    }

    if (stats.averageConnectionTime > this.config.connectionTimeout * 0.5) {
      recommendations.push('High average connection time. Consider optimizing database queries or network configuration.');
    }

    return recommendations;
  }

  /**
   * Get configuration for current environment and tier
   */
  getConfiguration(): ProductionConnectionPoolConfig {
    return { ...this.config };
  }

  /**
   * Graceful shutdown with production considerations
   */
  async shutdown(): Promise<void> {
    console.log('Starting graceful shutdown of production connection pool...');
    this.isShuttingDown = true;
    
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
    
    if (this.autoScalingTimer) {
      clearInterval(this.autoScalingTimer);
    }

    // Wait for active connections to finish (with timeout)
    const shutdownTimeout = 30000; // 30 seconds
    const startTime = Date.now();
    
    while (this.stats.activeConnections > 0 && (Date.now() - startTime) < shutdownTimeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.emit('shutdown', {
      finalStats: this.getStats(),
    });
    
    console.log('Production connection pool shutdown completed');
  }
}

// Singleton instance
export const productionConnectionPool = new ProductionDatabaseConnectionPool();

export default productionConnectionPool;
