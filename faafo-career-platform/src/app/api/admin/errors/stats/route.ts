import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      );
    }

    // Check user role for admin privileges
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    });

    const isAdmin = user?.role === 'ADMIN' || user?.role === 'SUPER_ADMIN';

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    const now = new Date();
    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Get total errors in last 24 hours
    const totalErrors = await prisma.errorLog.count({
      where: {
        timestamp: {
          gte: twentyFourHoursAgo,
        },
      },
    });

    // Get total requests (approximate from user sessions or page views)
    // For now, we'll use a simple calculation based on error logs and assume a baseline
    const totalRequests = Math.max(totalErrors * 10, 1000); // Rough estimate
    const errorRate = (totalErrors / totalRequests) * 100;

    // Get top errors by frequency
    const topErrorsRaw = await prisma.errorLog.groupBy({
      by: ['message'],
      where: {
        timestamp: {
          gte: twentyFourHoursAgo,
        },
      },
      _count: {
        message: true,
      },
      orderBy: {
        _count: {
          message: 'desc',
        },
      },
      take: 10,
    });

    const topErrors = topErrorsRaw.map(error => ({
      message: error.message,
      count: error._count.message,
    }));

    // Get errors by hour for the last 24 hours
    const errorsByHour = [];
    for (let i = 23; i >= 0; i--) {
      const hourStart = new Date(now.getTime() - i * 60 * 60 * 1000);
      const hourEnd = new Date(hourStart.getTime() + 60 * 60 * 1000);
      
      const count = await prisma.errorLog.count({
        where: {
          timestamp: {
            gte: hourStart,
            lt: hourEnd,
          },
        },
      });

      errorsByHour.push({
        hour: hourStart.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: false 
        }),
        count,
      });
    }

    // Get affected users count
    const affectedUsers = await prisma.errorLog.groupBy({
      by: ['userId'],
      where: {
        timestamp: {
          gte: twentyFourHoursAgo,
        },
        userId: {
          not: null,
        },
      },
    });

    return NextResponse.json({
      totalErrors,
      errorRate,
      topErrors,
      errorsByHour,
      affectedUsers: affectedUsers.length,
      timeRange: '24h',
      lastUpdated: now.toISOString(),
    });

  } catch (error) {
    console.error('Error fetching error statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch error statistics' },
      { status: 500 }
    );
  }
}
